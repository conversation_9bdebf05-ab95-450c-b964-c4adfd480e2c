# DCAP 分类引擎表容量功能实现文档

## 项目背景和上下文

### 项目概述
`dcap_classifier_engine` 是一个数据分类引擎项目，用于扫描和分类各种数据库中的敏感数据。该引擎支持多种数据库类型（MySQL、PostgreSQL、SQL Server、Oracle等），并能够识别和标记敏感数据列。

### 当前任务目标
在现有的表行数统计功能基础上，新增**表容量统计功能**，用于记录每个表的存储空间大小（以字节为单位）。这个功能将帮助用户了解数据表的存储占用情况。

### 代码架构概述
项目采用抽象工厂模式：
- **`RuleContext`** 抽象基类：定义通用的数据库操作接口
- **具体实现类**：如 `MySqlRuleContext`、`PostgreSqlRuleContext` 等，针对不同数据库提供特定实现
- **配置参数**：通过 `TaskParam.tableCapacityEnabled` 控制是否启用表容量统计

## 已完成的工作详细记录

### 第一步：RuleContext 抽象层实现 ✅

#### 1.1 添加抽象方法
在 `src/main/java/com/dcap/classifier/context/RuleContext.java` 中添加：

```java
/**
 * 获取表容量查询SQL，子类可以重写此方法提供数据库特定的表容量查询逻辑
 * 默认返回null，表示不支持表容量查询
 */
protected String getTableCapacitySQLQuery(String qualifiedTableName) {
    return null; // 默认不支持，子类重写
}
```

#### 1.2 添加表容量获取方法
```java
/**
 * 获取表容量大小（字节）
 */
protected long getTableCapacity(String qualifiedTableName) throws InitializationException {
    // 包含完整的错误处理逻辑，失败时返回0而不抛异常
}

public long getTableCapacity() {
    // 获取当前表容量的便捷方法
}
```

#### 1.3 修改 evaluateRules 方法
在表扫描流程中添加表容量获取逻辑：
```java
// 表容量大小
Long tableCapacity = null;
boolean isTableCapacityEnabled = this.getTaskGlobalDataHolder().getTaskParam().isTableCapacityEnabled();
if (isTableCapacityEnabled) {
    tableCapacity = this.getTableCapacity();
}
```

### 第三步：recordInventory 方法更新 ✅

#### 3.1 修改方法签名
```java
// 原方法签名
public void recordInventory(Long tableRowCount, Long tenantId, String dataSourceId, 
                           Map<String, List<MatchedResult>> matchedResultMap)

// 新方法签名  
public void recordInventory(Long tableRowCount, Long tableCapacity, Long tenantId, String dataSourceId,
                           Map<String, List<MatchedResult>> matchedResultMap)
```

#### 3.2 更新数据写入逻辑
- **CSV 格式**：在 entries 数组末尾添加 `capacity` 字段
- **JSON 格式**：在 map 中添加 `"tableCapacity": capacity` 字段

#### 3.3 更新所有子类实现
已更新以下文件的 `recordInventory` 方法：
- `src/main/java/com/dcap/classifier/context/SinodbRuleContext.java`
- `src/main/java/com/dcap/classifier/context/ImpalaRuleContext.java`
- `src/main/java/com/dcap/classifier/context/Gbase8sRuleContext.java`

### MySQL 表容量实现 ✅

#### 实现位置
`src/main/java/com/dcap/classifier/context/MySqlRuleContext.java`

#### 实现代码
```java
@Override
protected String getTableCapacitySQLQuery(String qualifiedTableName) {
    Matcher matcher = pattern.matcher(qualifiedTableName);
    String schema = null;
    String tableName = null;
    if (matcher.matches()) {
        schema = matcher.group(1);
        tableName = matcher.group(2);
    }
    // 使用 data_length + index_length 获取表的总容量
    StringBuilder sql = new StringBuilder("SELECT (data_length + index_length) AS table_capacity " +
            "FROM information_schema.tables " +
            "WHERE table_schema = '"+schema+"' AND table_name = '"+tableName+"';");
    return sql.toString();
}
```

#### MySQL 实现说明
- **数据源**：`information_schema.tables` 系统表
- **容量计算**：`data_length + index_length`（数据大小 + 索引大小）
- **表名解析**：复用现有的正则表达式 pattern
- **优势**：高效、准确，无需扫描实际数据

## 技术实现细节

### 设计模式
参考现有的表行数实现模式：
1. **抽象层**：在 `RuleContext` 中定义通用接口
2. **具体实现**：各数据库子类重写 `getTableCapacitySQLQuery` 方法
3. **错误处理**：统一在抽象层处理，失败时返回 0
4. **配置控制**：通过 `TaskParam.tableCapacityEnabled` 开关控制

### 错误处理策略
- 查询失败时返回 0，不影响主扫描流程
- 不支持的数据库返回 0
- 权限不足时返回 0
- 确保程序健壮性，不因表容量查询失败而中断整个扫描

### 数据流程
1. 检查 `tableCapacityEnabled` 配置
2. 调用 `getTableCapacity()` 方法
3. 子类提供具体的 SQL 查询实现
4. 将结果写入 inventory 记录
5. 在日志中显示表容量信息

## 修改文件列表

### 核心文件
- `src/main/java/com/dcap/classifier/context/RuleContext.java` - 抽象层实现
- `src/main/java/com/dcap/classifier/context/MySqlRuleContext.java` - MySQL 具体实现

### 子类更新
- `src/main/java/com/dcap/classifier/context/SinodbRuleContext.java`
- `src/main/java/com/dcap/classifier/context/ImpalaRuleContext.java`  
- `src/main/java/com/dcap/classifier/context/Gbase8sRuleContext.java`

### 配置文件
- `src/main/java/com/yd/dcap/probe/client/TaskConfig.java` - 已有 `tableCapacityEnabled` 字段

## 下一步工作计划

### 待实现的数据库

#### PostgreSQL
- **实现文件**：`src/main/java/com/dcap/classifier/context/PostgreSqlRuleContext.java`
- **实现方案**：使用 `pg_total_relation_size()` 函数
- **SQL 示例**：`SELECT pg_total_relation_size('schema.table'::regclass)`

#### SQL Server  
- **实现文件**：`src/main/java/com/dcap/classifier/context/MsSqlServerRuleContext.java`
- **实现方案**：查询系统表获取数据页和索引页大小
- **SQL 示例**：使用 `sys.dm_db_partition_stats` 视图

#### Oracle
- **实现文件**：`src/main/java/com/dcap/classifier/context/OracleRuleContext.java`
- **实现方案**：使用 `user_segments` 或 `dba_segments` 视图
- **SQL 示例**：`SELECT SUM(bytes) FROM user_segments WHERE segment_name = 'TABLE_NAME'`

#### 其他数据库
- DB2、Hana、ClickHouse、Trino 等根据各自特点实现

### 实现优先级
建议按以下顺序实现：
1. PostgreSQL（使用广泛）
2. SQL Server（企业常用）
3. Oracle（企业级数据库）
4. 其他数据库

## AI 角色定位和继续开发指南

### 当前项目状态
✅ **已完成**：
- RuleContext 抽象层的表容量功能框架
- recordInventory 方法的完整更新
- MySQL 表容量查询的具体实现
- 所有相关子类的方法签名更新

🔄 **进行中**：
- 其他数据库的表容量查询实现

### AI 角色定位
你是一个专业的 Java 后端开发工程师，正在为 dcap_classifier_engine 项目实现表容量统计功能。你需要：

1. **遵循现有代码风格**：参考 MySQL 实现和表行数实现模式
2. **保持架构一致性**：在各数据库子类中重写 `getTableCapacitySQLQuery` 方法
3. **确保错误处理**：失败时返回 0，不影响主流程
4. **注重性能**：优先使用系统表/视图，避免全表扫描
5. **测试编译**：每次修改后运行 `mvn compile` 确保代码正确

### 开发规范
- 使用中文注释和日志
- 不使用 Lombok（项目要求）
- 每次修改后进行编译测试
- 参考现有实现模式，保持代码一致性
- 优先考虑数据库特定的高效查询方式

### 继续开发建议
1. 选择下一个要实现的数据库（建议 PostgreSQL）
2. 研究该数据库的表容量查询最佳实践
3. 在对应的 RuleContext 子类中实现 `getTableCapacitySQLQuery` 方法
4. 测试编译确保代码正确
5. 重复以上步骤直到所有主要数据库都实现完成

---

**注意**：本文档记录了表容量功能的完整实现过程，新的 AI 会话可以基于此文档无缝继续开发工作。
