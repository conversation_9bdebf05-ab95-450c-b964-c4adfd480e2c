{"taskParam": {"dcapProbeId": 1, "dbInstAddressId": 83869, "dbInstAccountId": 28223, "viewDefinitionSampling": false, "sampleCount": 100, "sampleSqlTimeout": 15, "minimumLength": 0, "maximumLength": 256, "hitPercentage": 60, "tableType": "TABLE", "dataType": "TEXT,NUMBER", "excludeSchema": "", "excludeTable": "", "excludeColumn": "", "tableRowCountEnabled": false, "tableCapacityEnabled": true, "tableRowCount": 100, "tableRowCountUnit": 1, "excludeEmptyValues": false, "emptyPercentage": 90, "scanRange": {"excludedDatabase": [], "excludedSchema": [], "excludedSynonym": [], "excludedTable": [], "excludedView": [], "selectedDatabase": [], "selectedSchema": [], "selectedSynonym": [], "selectedTable": [], "selectedView": []}}, "datasource": {"dbNameType": 2, "sourceType": "pgsql", "port": "30159", "name": "kingbase", "host": "app-alpha.yuandiansec.net", "authCfg": {"password": "123456", "username": "system"}, "id": 28225, "extraCfg": "test"}, "tenantId": 1, "name": "default-db-28225", "policies": [], "scanJobHistoryId": 86125, "taskId": 82902}