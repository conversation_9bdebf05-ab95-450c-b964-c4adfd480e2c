package com.dcap.classifier.context;

import com.dcap.classifier.*;
import com.dcap.classifier.access.DataSampler;
import com.dcap.classifier.access.SQLDataPager;
import com.dcap.classifier.access.TimeoutException;
import com.dcap.classifier.analyzer.FingerprintAnalyzer;
import com.dcap.classifier.analyzer.IntelligentAnalyzer;
import com.dcap.classifier.analyzer.LineageAnalyzer;
import com.dcap.classifier.analyzer.RecognitionResultCache;
import com.dcap.classifier.context.privilege.DatabaseUser;
import com.dcap.classifier.context.privilege.DbPrivilege;
import com.dcap.classifier.rules.ColumnJniComplexRuleEvaluator;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.GuardClassifierJniComplexRule;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.JSON;
import com.dcap.utils.Messages;
import com.google.common.util.concurrent.RateLimiter;
import com.yd.dcap.classifier.DynamoRuleContext;
import com.yd.dcap.classifier.ElasticsearchRuleContext;
import com.yd.dcap.classifier.MongoRuleContext;
import com.yd.dcap.classifier.RedisRuleContext;
import com.yd.dcap.classifier.config.SpringContextUtil;
import com.yd.dcap.classifier.taskreport.ScanDbReport;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.classifier.taskreport.TaskReport;
import com.yd.dcap.common.utils.UtilMisc;
import com.yd.rules.engine.result.MatchedResult;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.postgresql.jdbc.PgConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.io.IOException;
import java.sql.*;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

public abstract class RuleContext {

    protected final Logger LOG = LoggerFactory.getLogger(this.getClass().getSimpleName());
    protected static final String SELECT_COUNT = "select count(*) from ";
    protected static final String MATCHING_RESULT_DELIMITER = "_A_";

    protected final boolean scanResultRecordAsTable;



    /*比较列的大小，*/
    private static final Comparator<ContextColumn> PK_COLUMN_BY_ORDINAL = new Comparator<ContextColumn>() {
        public int compare(ContextColumn column1, ContextColumn column2) {
            // 结果为负数，说明 column1 比 column2 小，结果为正数，说明 column1 比 column2 大
            return column1.getPkOrdinal() - column2.getPkOrdinal();
        }
    };
    protected final TaskGlobalDataHolder globalDataHolder;
    protected List<String> catalogs;
    protected List<ContextTable> tables;
    protected String instanceName;

    /**
     * 当前表在 tables 列表中的索引
     */
    protected Integer currentTable;
    private Boolean catalogUsed;
    private Boolean catalogAtStart;
    private Boolean schemaUsed;
    private TreeMap<String, List<String>> tokenizedColumnSamples;
    private String catalogSeparator;
    /**
     * 数据库的引用标识符，例如：[`] 可用来处理特殊字符。
     */
    private String quote;
    private int conOpen;
    private int conClose;
    // 采样器 key 是 tableName
    private HashMap<String, DataSampler> samplers;
    /**
     * key 是 tableName
     */
    private Map<String, Map<String, List<String>>> samples;
    private ClassifierDataSource datasource;
    private Integer currentCatalog;
    private List<ContextColumn> tableColumns;
    private List<ContextColumn> primaryKeyColumns;
    private ContextTable tableColumnsTable;
    private Connection con;

    private final List<IntelligentAnalyzer> analyzers;

    public RuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        Environment environment = SpringContextUtil.getBean(Environment.class);
        if (environment != null){
            scanResultRecordAsTable = environment.getProperty("custom.mq.sdi-scan-result-record-as-table", Boolean.class, true);
        } else {
            scanResultRecordAsTable = false;
        }
        this.datasource = dataSource;
        this.globalDataHolder = globalDataHolder;
        this.setConnection(this.datasource.connect());
        // 初始化智能分析器
        List<IntelligentAnalyzer> analyzerList = new ArrayList<>();
        
        // 根据任务参数决定是否添加智能指纹分析器
        if (this.globalDataHolder.getTaskParam().getIntelligentFingerprintEnabled()) {
            LOG.info("已启用智能指纹识别分析器");
            analyzerList.add(new FingerprintAnalyzer());
        }
        
        // 根据任务参数决定是否添加智能血缘分析器
        if (this.globalDataHolder.getTaskParam().getIntelligentLineageEnabled()) {
            LOG.info("已启用智能血缘识别分析器");
            analyzerList.add(new LineageAnalyzer());
        }
        
        this.analyzers = analyzerList;
    }

    public static RuleContext createRuleContext(ClassifierDataSource dataSource, TaskGlobalDataHolder globalDataHolder) throws InitializationException, ConnectionException {
        if (dataSource != null) {
            switch (dataSource.getType()) {
                case MSSQL:
                    return new MsSqlServerRuleContext(dataSource, globalDataHolder);
                case OCEANBASE_ORACLE:
                    return new OceanbaseOracleRuleContext(dataSource, globalDataHolder);
                case ORACLE:
                    return new OracleRuleContext(dataSource, globalDataHolder);
                case MONGODB:
                    return new MongoRuleContext(dataSource, globalDataHolder);
                case DORIS:
                    return new DorisRuleContext(dataSource, globalDataHolder);
                case DYNAMODB:
                    return new DynamoRuleContext(dataSource, globalDataHolder);
                case MYSQL:
                    return new MySqlRuleContext(dataSource, globalDataHolder);
                case DAMENG:
                    return new DaMengRuleContext(dataSource, globalDataHolder);
                case POSTGRESQL:
                case REDSHIFT:
                    return new PostgreSqlRuleContext(dataSource, globalDataHolder);
                case HANA:
                    return new HanaRuleContext(dataSource, globalDataHolder);
                case SPARK_SQL:
                    return new SparkSqlRuleContext(dataSource, globalDataHolder);
                case DB2:
                    return new Db2RuleContext(dataSource, globalDataHolder);
                case MAXCOMPUTE:
                    return new MaxComputeRuleContext(dataSource, globalDataHolder);
                case IMPALA:
                    return new ImpalaRuleContext(dataSource, globalDataHolder);
                case DREMIO:
                    return new DremioRuleContext(dataSource, globalDataHolder);
                case SINODB:
                    return new SinodbRuleContext(dataSource, globalDataHolder);
                case GBASE8S:
                    return new Gbase8sRuleContext(dataSource, globalDataHolder);
                case GBASE8A:
                    return new Gbase8aRuleContext(dataSource, globalDataHolder);
                case ELASTIC_SEARCH:
                    return new ElasticsearchRuleContext(dataSource, globalDataHolder);
                case REDIS:
                    return new RedisRuleContext(dataSource, globalDataHolder);
                case CLICKHOUSE:
                    return new ClickhouseRuleContext(dataSource, globalDataHolder);
                case TRINO:
                    return new TrinoRuleContext(dataSource, globalDataHolder);
                case SYBASE:
                    return new SybaseRuleContext(dataSource, globalDataHolder);
                default:
                    return new GenericRuleContext(dataSource, globalDataHolder);
            }
        } else {
            throw new InitializationException(Messages.getString("Could not access datasource: '${datasourceName}' for Classification Process: '${objectDesc}'", "datasourceName", String.valueOf(dataSource)));
        }
    }

    public static List<String> uppercase(List<String> columnValues) {
        return columnValues.stream().filter(Objects::nonNull).map(String::toUpperCase).collect(Collectors.toList());
    }

    /**
     * 将每一列的值进行分词。使用空格分词。
     *
     * @param columnValues
     * @return
     */
    public static List<String> tokenize(List<String> columnValues) {
        String whitespace = " ";
        return columnValues.stream().filter(Objects::nonNull).map(String::trim).flatMap(v -> Arrays.stream(v.split(whitespace)).distinct()).distinct().collect(Collectors.toList());
    }

    public List<String> getModifiedSampleFor(String columnName) {
        return this.tokenizedColumnSamples == null ? null : this.tokenizedColumnSamples.get(columnName);
    }

    public List<String> createModifiedSampleFor(String columnName, boolean toTokenize, boolean toUppercase, List<String> columnValues) throws ClassifierException {
        if (this.tokenizedColumnSamples == null) {
            this.tokenizedColumnSamples = new TreeMap<>();
        }

        List<String> tokenizedColumnValues = columnValues;
        if (toTokenize) {
            tokenizedColumnValues = tokenize(columnValues);
        }

        if (toUppercase) {
            // 这里要用 tokenizedColumnValues 是要用分词后的结果。
            tokenizedColumnValues = uppercase(tokenizedColumnValues);
        }

        tokenizedColumnSamples.put(columnName, tokenizedColumnValues);
        return tokenizedColumnValues;
    }

    public ColumnIterator getColumnProvider(String catalog, String schemaPattern, String tableNamePattern, DatabaseMetaData metaData) throws SQLException, ClassifierException {
        return new JDBCColumnIterator(catalog, schemaPattern, tableNamePattern, metaData);
    }

    public ColumnIterator getColumnProvider(String catalog, String schemaPattern, String tableNamePattern, String columnNamePattern, DatabaseMetaData metaData) throws SQLException, ClassifierException {
        return new JDBCColumnIterator(catalog, schemaPattern, tableNamePattern, columnNamePattern, metaData);
    }

    public DataSampler getSamplerFor(String tableName) throws ClassifierException {
        if (this.samplers == null) {
            this.samplers = new HashMap<>(10);
        }

        if (this.samplers.containsKey(tableName)) {
            return this.samplers.get(tableName);
        } else {
            DataSampler dataSampler = new DataSampler(this);
            this.samplers.put(tableName, dataSampler);
            return dataSampler;
        }
    }

    public Map<String, List<String>> getSampleFor(String tableName) throws ClassifierException {
        if (this.samples == null) {
            this.samples = new HashMap<>(10);
        }

        if (this.samples.containsKey(tableName)) {
            return this.samples.get(tableName);
        }

        List<ContextColumn> columns = getTableColumns();
        // 获取指定表的采样器
        DataSampler dataSampler = getSamplerFor(tableName);
        // 进行数据采样
        Map<String, List<String>> sampleData = null;
        try {
            sampleData = dataSampler.sample(columns);
        } catch (Exception e) {
            e.printStackTrace();
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(
                            StatusRecord.Position.Sampling, "{\"tableName\":\"" + tableName + "\"}", null, e
                    );
        }
        if (sampleData == null) {
            sampleData = new HashMap<>();
        }
        this.samples.put(tableName, sampleData);
        return sampleData;
    }

    public void cleanupSamples() {
        this.samplers = null;
        this.samples = null;
//        this.tokenizedColumnSamples = null;
    }

    public boolean isCatalogUsed() {
        if (this.catalogUsed != null) {
            return this.catalogUsed;
        }
        try {
            DatabaseMetaData metaData = this.getConnection().getMetaData();
            this.catalogUsed = metaData.supportsCatalogsInDataManipulation() ? Boolean.TRUE : Boolean.FALSE;
        } catch (SQLException sqlException) {
            LOG.error("Could not determine catalog use: " + this.getDatasource(), sqlException);
            this.catalogUsed = Boolean.FALSE;
        }
        return this.catalogUsed;
    }

    public boolean isCatalogAtStart() throws SQLFeatureNotSupportedException {
        if (this.catalogAtStart != null) {
            return this.catalogAtStart;
        }
        try {
            DatabaseMetaData metaData = this.getConnection().getMetaData();
            this.catalogAtStart = metaData.isCatalogAtStart() ? Boolean.TRUE : Boolean.FALSE;
        } catch (java.sql.SQLFeatureNotSupportedException sqlException) {
            LOG.error("Could not determine catalog use: " + this.getDatasource(), sqlException);
            this.catalogAtStart = Boolean.FALSE;
            throw sqlException;
        } catch (SQLException sqlException) {
            LOG.error("Could not determine catalog use: " + this.getDatasource(), sqlException);
            this.catalogAtStart = Boolean.FALSE;
        }
        return this.catalogAtStart;
    }

    public boolean isSchemaUsed() {
        if (this.schemaUsed != null) {
            return this.schemaUsed;
        }
        try {
            DatabaseMetaData metaData = this.getConnection().getMetaData();
            this.schemaUsed = metaData.supportsSchemasInDataManipulation() ? Boolean.TRUE : Boolean.FALSE;
        } catch (SQLException sqlException) {
            LOG.error("Could not determine catalog use: " + this.getDatasource(), sqlException);
            this.schemaUsed = Boolean.FALSE;
        }
        return this.schemaUsed;
    }

    public String getCatalogSeparator() {
        if (this.catalogSeparator != null) {
            return this.catalogSeparator;
        }
        try {
            DatabaseMetaData metaData = this.getConnection().getMetaData();
            this.catalogSeparator = metaData.getCatalogSeparator();
        } catch (SQLException sqlException) {
            LOG.error("Could not determine catalog use: {}", this.getDatasource(), sqlException);
            this.catalogSeparator = ".";
        }
        return this.catalogSeparator;
    }

    public String getQuote() {
        if (this.quote != null) {
            return this.quote;
        }

        try {
            this.quote = "";
            DatabaseMetaData metaData = this.getConnection().getMetaData();
            if (null != metaData) {
                this.quote = metaData.getIdentifierQuoteString();
            }
            if (Objects.equals(this.quote, " ")){
                this.quote = "`";
            }

            if (this.quote == null) {
                this.quote = "";
            }
        } catch (Exception sqlException) {
            LOG.error("Could not determine catalog use: {}", this.getDatasource(), sqlException);
            this.quote = "";
        }
        return this.quote;
    }

    protected ContextTable createContextTable(String catalog, String schema, String tableName, String tableTypeName, String tableComment) {
        ContextTable contextTable = null;
        try {
            contextTable = new ContextTable(this.cleanName(catalog), this.cleanName(schema), this.cleanName(tableName), this.cleanName(tableTypeName), this.cleanName(tableComment));
        } catch (TypeNotSupportedException exception) {
            LOG.warn("Table Type is unknown: '" + tableTypeName + "' for: " + (catalog != null ? catalog + "." : "") + (schema != null ? schema + "." : "") + tableName + " on: " + this.getConnectionDescriptor() + ". Skipping the table...", exception);
        }
        return contextTable;
    }

    protected ContextColumn createContextColumn(ContextTable table, String columnName, int datatype, String typename, int size, int scale,
                                                String remarks, int position, boolean isPrimaryKey) {
//        this.checkDutyCycle();
        ContextColumn column;
        try {
            column = new ContextColumn(table, this.cleanName(columnName), datatype, this.cleanName(typename), size,scale, remarks, position, isPrimaryKey);
        } catch (TypeNotSupportedException var9) {
            LOG.error("未注册的列类型：datatype: [{}], typeName: [{}]", datatype, typename);
            LOG.warn("Type of: '{}' is unknown.  Trying data type name: '{}'.", datatype, typename);
            column = this.createContextColumn(table, columnName, typename, size, scale, remarks, position, isPrimaryKey);
        }
        return column;
    }

    protected ContextColumn createContextColumn(ContextTable table, String columnName,
                                                String typename, int size, int scale, String remarks, int position, boolean isPrimaryKey) {
        ContextColumn column = null;
        int datatype;
        try {
            // 根据数据类型名称获取数据类型
            datatype = ContextColumn.findJdbcType(typename);
            column = new ContextColumn(table, columnName, datatype, typename, size, scale, remarks, position, isPrimaryKey);
        } catch (Exception exception) {
//            exception.printStackTrace();
            LOG.warn("Column Type is unknown: '{}' on: '{}'. Skipping the column...", typename, table);
        }
        return column;
    }

    protected boolean hasMoreCatalogs() {
        LOG.debug("has More Catalogs");
        boolean hasMoreCatalogs;
        if (this.catalogs == null) {
            List<String> catalogs = this.findCatalogs();
            hasMoreCatalogs = catalogs != null && catalogs.size() > 0;
        } else if (this.currentCatalog == null) {
            hasMoreCatalogs = !this.catalogs.isEmpty();
        } else {
            int currentCatalog = this.currentCatalog;
            int catalogsSize = this.catalogs.size();
            hasMoreCatalogs = currentCatalog < catalogsSize - 1;
        }
        return hasMoreCatalogs;
    }

    protected boolean hasMoreCatalogTables() {
        boolean hasMore;
        if (this.currentTable == null) {
            hasMore = this.tables != null && !this.tables.isEmpty();
        } else {
            hasMore = this.currentTable < this.tables.size() - 1;
        }
        return hasMore;
    }

    public boolean hasMoreTables() {
        LOG.debug("has More Tables");
        return this.hasMoreCatalogTables() || this.hasMoreCatalogs();
    }

    protected void resetBrowse() {
        if (this.catalogs != null) {
            this.catalogs.clear();
        }

        this.catalogs = new ArrayList<>();
        this.currentCatalog = null;
        this.currentTable = null;
    }

    public boolean hasCurrentTable() {
        return this.currentTable != null;
    }

    public ContextTable getCurrentTable() {
        return this.currentTable == null ? this.getNextTable() : this.tables.get(this.currentTable);
    }

    /**
     * 可以在这个方法中，添加要忽略的 catalog/schema
     * 这样在 findCatalogs 方法中，会跳过添加到 catalogs 集合
     */
    protected Set<String> getEmptyCatalogs() {
        return this.getDatasource().getEmptyCatalogs();
    }

    protected List<String> findCatalogs() {
        LOG.info("find Catalogs");
        this.resetBrowse();
        String dbName = this.getDatasource().getDbName();
        if (dbName != null && !dbName.isEmpty()) {
            this.catalogs.add(dbName);
            return this.catalogs;
        }

        Set<String> emptyCatalogs = this.getEmptyCatalogs().stream().map(String::toUpperCase).collect(Collectors.toCollection(HashSet::new));
        try {
            Connection connection = this.getConnection();
            if (connection.isClosed()){
                LOG.warn("connection is closed");
                connection = this.getConnection();
            } else{
                LOG.info("connection is open");
            }

            DatabaseMetaData metaData = connection.getMetaData();
            Set<String> selectedSchemaOrDatabase;
            Set<String> excludedSchemaOrDatabase;
            String inventoryDbName = this.instanceName;
            if (StringUtils.isBlank(inventoryDbName)) {
                inventoryDbName = this.getDatasource().getInventoryDbName();
            }
            ResultSet resultSet;
            String columnLabel;
            if (this.getDatasource().isCatalog()) {
                resultSet = metaData.getCatalogs();
                columnLabel = Constants.DBMD_TABLE_CAT;
                // database
                selectedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
                excludedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            } else {
                resultSet = metaData.getSchemas();
                columnLabel = Constants.DBMD_TABLE_SCHEM;
                // schema
                selectedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
                excludedSchemaOrDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            }
            try {
                while (resultSet.next()) {
                    String catalogName = resultSet.getString(columnLabel);
                    if (!emptyCatalogs.isEmpty() && emptyCatalogs.contains(catalogName.toUpperCase())) {
                        continue;
                    }
                    String catalogIdentityName = inventoryDbName != null ?
                            (inventoryDbName.toLowerCase() + "." + catalogName.toLowerCase()) : catalogName.toLowerCase();
                    // 如果是排除，就跳过。必须包含
                    if (excludedSchemaOrDatabase.contains(catalogIdentityName)){
                        continue;
                    }
                    if (selectedSchemaOrDatabase.isEmpty() || selectedSchemaOrDatabase.contains(catalogIdentityName)) {
                        this.catalogs.add(catalogName);
                    }
                }
                return this.catalogs;
            } finally {
                if (resultSet != null) {
                    resultSet.close();
                }
            }
        } catch (SQLException sqlException) {
            LOG.error("Could not get catalogs for '{}'{}{}", dbName, System.lineSeparator(), sqlException.getMessage(), sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                            .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogs,null,null,sqlException);
        }
        return this.catalogs;
    }

    protected int addTable(ContextTable table) {
        if (table == null) {
            return this.tables.size();
        }
        if (this.tables == null) {
            this.tables = new ArrayList<>();
        }

        // 获取表创建时间
        long tableCreateTime = fetchTableCreateTimestamp(this.getConnection(), table);
        table.setTableCreatedTimestamp(tableCreateTime);
        this.tables.add(table);
        return this.tables.size();
    }

    /**
     * 首先根据 connection 参数判断是什么数据库，只有 mysql 和 sqlserver 数据库才继续。
     * 然后查询数据库中，指定的 tableName 的创建时间，并且以 long 类型返回。
     * 这里注意要根据数据库类型采用不同的获取创建时间的实现
     * @param connection
     * @param table
     * @return
     */
    private long fetchTableCreateTimestamp(Connection connection, ContextTable table){
        long createTime = 0;
        if(connection == null){
            return createTime;
        }
        try {
            String databaseProductName = connection.getMetaData().getDatabaseProductName();
            if (org.apache.commons.lang3.StringUtils.isBlank(databaseProductName)){
                return createTime;
            }
            databaseProductName = connection.getMetaData().getDatabaseProductName().toLowerCase().replaceAll("\\s", "");
            if(databaseProductName.contains("mysql")){
                createTime = fetchTableCreateTimestampForMysql(connection, table.getCatalog(), table.getTableName());
            } else if(databaseProductName.contains("sqlserver")){
                createTime = fetchTableCreateTimestampForSqlServer(connection, table.getCatalog(), table.getTableName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return createTime;
    }

    /**
     * 获取 mysql 数据库中，指定的 tableName 的创建时间。
     * @param connection
     * @param tableName
     * @return
     */
    private long fetchTableCreateTimestampForMysql(Connection connection, String tableSchema, String tableName){
        long createTime = 0;
        String sql = "SELECT CREATE_TIME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='"+tableSchema+"' AND TABLE_NAME = '" + tableName + "'";
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                createTime = resultSet.getTimestamp("CREATE_TIME").getTime();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (statement != null) {
                    statement.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return createTime;
    }

    private long fetchTableCreateTimestampForSqlServer(Connection connection, String catalog, String tableName){
        long createTime = 0L;
        String sql = "SELECT create_date FROM \""+catalog+"\".sys.tables WHERE name = '" + tableName + "'";
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            if (resultSet.next()) {
                createTime = resultSet.getTimestamp("create_date").getTime();
            }
            resultSet.close();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return createTime;
    }


    protected int addTables(Collection<ContextTable> var1) {
        if (var1 != null) {
            if (this.tables == null) {
                this.tables = new ArrayList<>();
            }
            this.tables.addAll(var1);
        }
        return this.tables.size();
    }

    protected List<ContextTable> listCatalogTables() {
        return this.tables;
    }

    protected List<ContextTable> findCatalogTables(String catalog) {
        LOG.info("获取所有的表：{}", catalog);
        this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_DATASOURCE_COUNT, 1);
        List<ContextTable> catalogTables = this.findCatalogTables(catalog, new String[]{"TABLE", "VIEW", "MATERIALIZED VIEW", "ALIAS", "SYNONYM","MQTs"});
        this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_TABLE_COUNT, catalogTables.size()).sendToServer();
        return catalogTables;
    }

    protected List<ContextTable> findCatalogTables(String catalog, String[] tableTypes) {
        Clock clock = new Clock();
        String catalogName = null;
        String schemaName = null;
        if (this.getDatasource().isCatalog()) {
            catalogName = catalog;
        } else {
            schemaName = catalog;
        }

        if (this.tables != null) {
            this.tables.clear();
        } else {
            this.tables = new ArrayList<>();
        }

        // 获取该catalog下所有表的注释映射
        Map<String, String> tableComments = getTableComments(catalog);

        this.currentTable = null;
        ResultSet resultSet = null;
        try {
            // todo: 这里是调整一下传入的 tableTypes。
            tableTypes = this.getDatasource().getDriverInterface().adjustTableTypes(tableTypes, true, this.getConnection());
            DatabaseMetaData metaData = this.getConnection().getMetaData();
            // todo: this maybe very slow for wrong input
            LOG.debug("Get tables with catalog = {} and schema = {}", catalogName, schemaName);
            resultSet = metaData.getTables(catalogName, schemaName, null, tableTypes);
            LOG.debug("Get tables - end");
            Set<String> selectedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedDatabase();
            Set<String> excludedDatabase = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedDatabase();
            Set<String> selectedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
            Set<String> excludedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            Set<String> selectedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedTable();
            Set<String> excludedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedTable();
            Set<String> selectedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedView();
            Set<String> excludedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedView();
            String inventoryDbName = this.instanceName;
            if (StringUtils.isBlank(inventoryDbName)) {
                inventoryDbName = this.getDatasource().getInventoryDbName();
            }
            while (resultSet.next()) {
                try {
                    String tableType = resultSet.getString("TABLE_TYPE");
                    String tableName = resultSet.getString("TABLE_NAME");
                    String tableCatalog = resultSet.getString("TABLE_CAT");
                    String tableSchema = resultSet.getString(Constants.DBMD_TABLE_SCHEM);
                    String tableComment = resultSet.getString("REMARKS");

                    // 如果JDBC获取的注释为空，使用自定义查询的结果
                    if (StringUtils.isBlank(tableComment)) {
                        tableComment = tableComments.get(tableCatalog+"."+tableSchema + "." + tableName);
                    }

                    if (StringUtils.isBlank(tableCatalog) && StringUtils.isNotBlank(catalogName)) {
                        tableCatalog = catalogName;
                    }

                    // 当前表是属于内部 schema 或 内部 catalog 就跳过
                    if (isInternalSchema(this.getDatasource(), tableSchema)
                            || isInternalCatalog(this.getDatasource(), tableCatalog)) {
                        LOG.debug("Catalog: {}, Schema: {} has been skipped ", catalog, tableSchema);
                        continue;
                    }
                    ContextTable table = this.createContextTable(tableCatalog, tableSchema, tableName, tableType, tableComment);
                    if (table == null) {
                        continue;
                    }
                    String tableIdentityName = table.getIdentityName().toLowerCase();
                    String databaseIdentityName = tableIdentityName.substring(0, tableIdentityName.indexOf(".")).toLowerCase();
                    String schemaIdentityName = tableIdentityName.substring(0, tableIdentityName.lastIndexOf(".")).toLowerCase();
                    tableIdentityName = inventoryDbName == null ? tableIdentityName : (inventoryDbName + "." + tableIdentityName).toLowerCase();
                    databaseIdentityName = inventoryDbName == null ? databaseIdentityName : (inventoryDbName + "." + databaseIdentityName).toLowerCase();
                    schemaIdentityName = inventoryDbName == null ? schemaIdentityName : (inventoryDbName + "." + schemaIdentityName).toLowerCase();
                    String tableTypeName = table.getType().getTypeName();
                    if (excludedDatabase.contains(databaseIdentityName)){
                        continue;
                    }
                    if (StringUtils.isNotBlank(tableSchema) && excludedSchema.contains(schemaIdentityName)){
                        continue;
                    }
                    if ("TABLE".equals(tableTypeName) && excludedTable.contains(tableIdentityName)){
                        continue;
                    }
                    if ("VIEW".equals(tableTypeName) && excludedView.contains(tableIdentityName)){
                        continue;
                    }

                    if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                            && (selectedSchema.isEmpty() || (StringUtils.isNotBlank(tableSchema) && selectedSchema.contains(schemaIdentityName)))
                            && (selectedTable.isEmpty() || selectedTable.contains(tableIdentityName))
                            && "TABLE".equals(tableTypeName)) {
                        this.addTable(table);
                        clock.incrementCount();
                    } else if ((selectedDatabase.isEmpty() || selectedDatabase.contains(databaseIdentityName))
                            && (selectedSchema.isEmpty() || (StringUtils.isNotBlank(tableSchema) && selectedSchema.contains(schemaIdentityName)))
                            && (selectedView.isEmpty() || selectedView.contains(tableIdentityName))
                            && "VIEW".equals(tableTypeName)) {
                        this.addTable(table);
                        clock.incrementCount();
                    }
                } catch (Exception e){
                    String message = Messages.getString("Could not access table for: '${datasourceName}' on: '${datasourceUrl}'",
                            "datasourceName", String.valueOf(this.getDatasource().getDescriptor()),
                            "datasourceUrl", this.getConnectionDescriptor());
                    LOG.error(message, e);
                }
            }

            if (LOG.isInfoEnabled()) {
                String tableType = tableTypes == null ? "Catalog Tables" : RuleEvaluator.arrayToString(tableTypes);
                LOG.debug(catalog + ": " + clock.getElapsedMessageForCounter(tableType));
            }
        } catch (SQLException sqlException) {
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource().getDescriptor()), "datasourceUrl", this.getConnectionDescriptor());
            LOG.error(message, sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                            .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogTables,
                                    "catalog [" + catalog + "]",null,sqlException);
        } catch (NullPointerException nullPointerException) {
            StringBuilder logMsg = new StringBuilder();
            String message = Messages
                    .getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'",
                            "datasourceName", String.valueOf(this.getDatasource().getDescriptor()),
                            "datasourceUrl", this.getConnectionDescriptor());
            logMsg.append(message);
            throw new DBDriverException(clock.getElapsedMessage(logMsg.toString()), nullPointerException);
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (Exception ignored) {
                }
            }
        }
        // 根据表类型进行排序
        this.sortTables();
        return this.listCatalogTables();
    }

    protected void sortTables() {
        if (this.tables != null && !this.tables.isEmpty()) {
            this.tables.sort((t1, t2) -> {
                ContextTable.TableType type1 = t1.getTableType();
                ContextTable.TableType type2 = t2.getTableType();
                // 定义类型优先级
                int priority1 = getTableTypePriority(type1);
                int priority2 = getTableTypePriority(type2);
                return priority1 - priority2;
            });
        }
    }
    protected int getTableTypePriority(ContextTable.TableType type) {
        if (type == ContextTable.TABLE || type == ContextTable.SYSTEM_TABLE) {
            return 1;  // 实体表优先级最高
        } else if (type == ContextTable.SYNONYM) {
            return 2;  // 同义词次之
        } else if (type == ContextTable.VIEW) {
            return 3;  // 视图优先级最低
        } else {
            return 4;  // 未知类型放最后
        }
    }

    protected boolean isInternalSchema(ClassifierDataSource dataSource, String tableSchema) {
        return false;
    }

    boolean isInternalCatalog(ClassifierDataSource dataSource, String catalog) {
        return false;
    }

    protected String getNextCatalog() {
        if (!this.hasMoreCatalogs()) {
            return null;
        }
        if (this.currentCatalog == null) {
            this.currentCatalog = 0;
        } else {
            this.currentCatalog = this.currentCatalog + 1;
        }
        return this.catalogs.get(this.currentCatalog);
    }

    public ContextTable getNextTable() {
        ContextTable table = null;
        if (this.hasMoreCatalogTables()) {
            if (this.currentTable == null) {
                this.currentTable = 0;
            } else {
                this.currentTable = this.currentTable + 1;
            }
            table = this.tables.get(this.currentTable);
        } else {
            while (this.hasMoreCatalogs()) {
                this.findCatalogTables(this.getNextCatalog());
                if (!this.hasMoreCatalogTables()) {
                    continue;
                }
                if (this.currentTable == null) {
                    this.currentTable = 0;
                } else {
                    this.currentTable = this.currentTable + 1;
                }
                table = this.tables.get(this.currentTable);
                break;
            }
        }
        return table;
    }

    public List<ContextColumn> getPrimaryKeyColumns() throws InitializationException {
        if (!this.hasCurrentColumns()) {
            this.refreshColumns();
        }

        if (this.primaryKeyColumns == null) {
            this.primaryKeyColumns = this.findPrimaryKeyColumns();
        }

        return this.primaryKeyColumns;
    }

    public List<ContextColumn> getTableColumns() throws InitializationException {
        if (this.hasCurrentColumns()) {
            return this.tableColumns;
        }
        this.refreshColumns();
        return this.tableColumns;
    }

    public boolean hasCurrentColumns() {
        boolean var1 = false;
        ContextTable currentTable = this.getCurrentTable();
        if (currentTable != null) {
            var1 = currentTable.equals(this.tableColumnsTable);
        }
        return var1;
    }

    int refreshColumns() throws InitializationException {
        this.primaryKeyColumns = null;
        Clock clock = new Clock();
        this.tableColumns = this.findTableColumns();
        // 按列名排序
        this.tableColumns.sort((c1, c2) -> c1.getColumnName().compareToIgnoreCase(c2.getColumnName()));
        this.tableColumnsTable = this.getCurrentTable();
        LOG.info(clock.getElapsedMessage("fetch columns for "+this.tableColumnsTable.getTableName()));
        return this.tableColumns.size();
    }

    public ContextColumn findColumnByName(String columnName) throws InitializationException {
        for (ContextColumn column : this.getTableColumns()) {
            if (columnName.equalsIgnoreCase(column.getColumnName())) {
                return column;
            }
        }
        return null;
    }

    protected List<ContextColumn> findTableColumns() throws InitializationException {
        Clock clock = new Clock();

        ContextTable currentTable = this.getCurrentTable();
        if (currentTable.getReference() != null) {
            currentTable = currentTable.getReference();
        }
        Connection connection=null;
        List<ContextColumn> contextColumns = new ArrayList<>();
        try {
            String catalog = currentTable.getCatalog();
            String schemaPattern = currentTable.getSchema();
            String tableNamePattern = currentTable.getTableName();
            connection = this.getConnection();
            if (connection == null) {
                return contextColumns;
            }
            DatabaseMetaData metaData = connection.getMetaData();
            try (ColumnIterator columnIterator = this.getColumnProvider(catalog, schemaPattern, tableNamePattern, metaData)) {
                Map<String, Boolean> primaryColumns = getPrimaryColumns(metaData, catalog, schemaPattern, currentTable.getTableName());
                while (columnIterator.hasNext()) {
                    ColumnProvider columnProvider = columnIterator.next();
                    String columnName = columnProvider.getColumnName();
                    ContextColumn contextColumn = this.createContextColumn(currentTable, columnName,
                            columnProvider.getColumnDataType(), columnProvider.getColumnTypeName(),
                            columnProvider.getColumnSize(), columnProvider.getColumnScale(),
                            columnProvider.getColumnRemarks(), columnProvider.getOrdinalPosition(),
                            primaryColumns.get(columnName) == Boolean.TRUE);
                    if (contextColumn == null) {
                        continue;
                    }
                    contextColumns.add(contextColumn);
                    clock.incrementCount();
                }
            }
            return contextColumns;
        } catch (SQLException | NullPointerException | ClassifierException exception) {
            try {
                if (connection != null && !connection.isClosed()){
                    connection.close();
                }
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(StatusRecord.Position.FindTableColumns,
                            clock.getElapsedMessage(message), null, exception);
            return contextColumns;
        } catch (Exception exception) {
            try {
                if (connection != null && !connection.isClosed()){
                    connection.close();
                }
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            LOG.error(message, exception);
            this.globalDataHolder.getProbeClientTaskContext()
                    .reportErrorOccurredExecuting(
                            StatusRecord.Position.FindTableColumns, clock.getElapsedMessage(message), null, exception
                    );
            return contextColumns;
        }
    }

    protected Map<String,Boolean> getPrimaryColumns(DatabaseMetaData metaData, String catalog, String schema, String tableName) throws SQLException {

        Map<String,Boolean> result = new HashMap<>();
        if (this.globalDataHolder.getDataSource().getEncryptionSwitch() == null ||
                this.globalDataHolder.getDataSource().getEncryptionSwitch() == 0){
            return result;
        }

        try(ResultSet primaryKeys = metaData.getPrimaryKeys(catalog, schema, tableName);){
            while (primaryKeys.next()) {
                String columnName = primaryKeys.getString("COLUMN_NAME");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(columnName)){
                    result.put(columnName, true);
                }
            }
        } catch (Exception e){
            return result;
        }
        return result;
    }

    protected List<ContextColumn> findPrimaryKeyColumns() throws InitializationException {
        Clock clock = new Clock();
        ContextTable currentTable = this.getCurrentTable();

        try {
            DatabaseMetaData metaData = this.getConnection().getMetaData();
            try (ResultSet result = metaData.getPrimaryKeys(currentTable.getCatalog(), currentTable.getSchema(), currentTable.getTableName())) {
                List<ContextColumn> columns = new ArrayList<>();

                while (result.next()) {
                    String columnName = result.getString("COLUMN_NAME");
                    int keySeq = result.getInt("KEY_SEQ");
                    ContextColumn column = this.findColumnByName(columnName);
                    if (column != null) {
                        column.setPkOrdinal(keySeq);
                        columns.add(column);
                        clock.incrementCount();
                    }
                }

                columns.sort(PK_COLUMN_BY_ORDINAL);
                if (clock.getCount() > 0) {
                    LOG.debug(currentTable.toString() + ' ' + clock.getElapsedMessageForCounter("Primary Key Column(s)"));
                } else {
                    LOG.debug(clock.getElapsedMessage("Found no Primary Key Columns for: " + currentTable));
                }
                return columns;
            }
        } catch (SQLException | NullPointerException exception) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
            throw new InitializationException(clock.getElapsedMessage(message), exception);
        }
    }

    public ClassifierDataSource getDatasource() {
        return this.datasource;
    }

    public String toString() {
        StringBuilder text = new StringBuilder();
        text.append(this.datasource).append(' ');
        if (this.hasCurrentTable()) {
            text.append(this.getCurrentTable());
        }

        return text.toString();
    }

    protected String cleanName(String value) {
        if (value != null) {
            return value.trim();
        }
        return null;
    }

    public void closeConnection() {
        try {
            if (this.con != null) {
                this.con.close();
            }
        } catch (SQLException ignored) {
        }

        this.con = null;
        ++this.conClose;
    }

    public void createConnection(Connection connection) {
        if (connection == null) {
            throw new NullPointerException("Exception during create new connection");
        }
        this.con = connection;
        try {
            if (!connection.isClosed()) {
                ++this.conOpen;
                LOG.debug("==========================================New connection opened==========conOpen=>" + this.conOpen + " conClose=>" + this.conClose);
                int timeout = (this.getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout() + 10) * 1000;
                connection.setNetworkTimeout(Executors.newSingleThreadExecutor(), timeout);
            }
        } catch (SQLFeatureNotSupportedException ignored) {
        } catch (Exception exception) {
            // PostgreSql 没有实现这个方法。
            if (!(connection instanceof PgConnection) && !exception.getMessage().contains("not supported")) {
                LOG.info("Could not check if the connection is closed.");
                this.globalDataHolder.getProbeClientTaskContext()
                        .reportErrorOccurredExecuting(StatusRecord.Position.Scan, null,null, exception);
            }
        } catch (Error e){

        }
    }

    public Connection getConnection() {
        if (!this.isEmpty(this.con)){
            return this.con;
        }
        this.con = null;
        try {
            this.createConnection(this.getDatasource().connect());
        } catch (Exception exception) {
            LOG.error("Exception during create new connection", exception);
        }
        return this.con;
    }

    public void setConnection(Connection connection) {
        if (connection == null) {
            throw new NullPointerException("connection is null");
        }
        try {
            if (!connection.isClosed()) {
                int timeout = (this.getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout() + 10) * 1000;
                connection.setNetworkTimeout(Executors.newSingleThreadExecutor(), timeout);
            }
        } catch (SQLFeatureNotSupportedException ignored) {
        } catch (Exception e) {
            // PostgreSql 没有实现这个方法。
            if (!(connection instanceof PgConnection) && !e.getMessage().contains("not supported")) {
                this.globalDataHolder.getProbeClientTaskContext()
                        .reportErrorOccurredExecuting(StatusRecord.Position.Scan, null, null,e);
            }
        } catch (Error e){

        }
        this.con = connection;
        ++this.conOpen;
        this.hasMoreTables();
    }

    public boolean isEmpty(Connection connection) {
        if (connection == null) {
            return true;
        }
        try {
            return connection.isClosed();
        } catch (SQLException sqlException) {
            LOG.info("Could not check if the connection is closed.");
            return true;
        }
    }

    public PreparedStatement getPreparedStatement(Connection connection, String sql) throws SQLException {
        PreparedStatement preparedStatement = connection.prepareStatement(sql, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        preparedStatement.setQueryTimeout(15);
        return preparedStatement;
    }

    public PreparedStatement getPreparedStatement(String sql) throws SQLException {
        PreparedStatement preparedStatement = this.getConnection()
                .prepareStatement(sql, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        preparedStatement.setQueryTimeout(15);
        return preparedStatement;
    }

    public Statement getStatement() throws SQLException {
        return this.getConnection().createStatement();
    }

    public String getCurrentTableName() {
        return this.getCurrentTable().getIdentityName();
    }

    public String getColumnNameOnly(List<ContextColumn> var1) {
        StringBuilder var2 = new StringBuilder();
        String var3 = ", ";
        ContextColumn var5;
        for (Iterator<ContextColumn> var4 = var1.iterator(); var4.hasNext(); var2.append(var5.getColumnName())) {
            var5 = var4.next();
            if (var2.length() != 0) {
                var2.append(var3);
            }
        }
        return var2.toString();
    }

    public String getQualifiedName() {
        return this.getQualifiedName(this.getCurrentTable());
    }

    public String getQualifiedName(ContextTable contextTable) {
        String quoteIdentifiers = this.quoteIdentifiers(contextTable.getTableName());
        String schema = contextTable.getSchema();
        if (schema == null) {
            schema = "";
        }

        String catalog = contextTable.getCatalog();
        if (catalog == null) {
            catalog = "";
        }

        StringBuilder qualifiedName = new StringBuilder(quoteIdentifiers.length() + schema.length() + catalog.length() + 6);
        boolean isCatalogUsed = this.isCatalogUsed() && catalog.length() > 0;
        boolean var7 = false;
        try {
            var7 = isCatalogUsed && this.isCatalogAtStart();
        } catch (SQLFeatureNotSupportedException e) {
            isCatalogUsed = false;
            this.catalogUsed = false;
        }
        if (var7) {
            qualifiedName.append(this.quoteIdentifiers(catalog)).append(this.getCatalogSeparator());
        }

        if (this.isSchemaUsed() && schema.length() > 0) {
            qualifiedName.append(this.quoteIdentifiers(schema)).append('.');
        }

        qualifiedName.append(quoteIdentifiers);
        if (isCatalogUsed && !var7) {
            qualifiedName.append(this.getCatalogSeparator()).append(this.quoteIdentifiers(catalog));
        }

        return qualifiedName.toString();
    }

    public String getQualifiedName(ContextColumn contextColumn) {
        StringBuilder qualifiedName = new StringBuilder();
        ContextTable contextTable = contextColumn.getTable();
        if (contextTable != null) {
            String tableName = contextTable.getTableName();
            if (tableName != null && tableName.length() > 0) {
                qualifiedName.append(this.getQualifiedName(contextTable)).append('.');
            }
        }

        qualifiedName.append(this.quoteIdentifiers(contextColumn.getColumnName()));
        return qualifiedName.toString();
    }

    public String getQualifiedName(List<ContextColumn> contextColumnList) {
        StringBuilder qualifiedName = new StringBuilder();
        String var3 = ", ";
        for (ContextColumn column : contextColumnList) {
            if (qualifiedName.length() > 0) {
                qualifiedName.append(var3);
            }
            qualifiedName.append(this.getQualifiedName(column));
        }
        return qualifiedName.toString();
    }

    protected String getCountSQLQuery(String qualifiedTableName) {
//        select count(1) from (select 1 from my_table limit 51) a
        if (this.getTaskGlobalDataHolder().getTaskParam().isTableRowCountEnabled()) {
            StringBuilder sql = new StringBuilder("select count(1) from (select 1 from ");
            sql.append(qualifiedTableName).append(" limit ");
            long rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getTableRowCountLimit();
            sql.append(rowCountValue).append(") a");
            return sql.toString();
        } else {
            StringBuilder sql = new StringBuilder("select count(1) from (select 1 from ");
            sql.append(qualifiedTableName).append(" limit ");
            int rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getSampleCount() + 1;
            sql.append(rowCountValue).append(") a");
            return sql.toString();
//            return SELECT_COUNT + qualifiedTableName;
        }
    }

    /**
     * 获取表容量查询SQL，子类可以重写此方法提供数据库特定的表容量查询逻辑
     * 默认返回null，表示不支持表容量查询
     *
     * @param qualifiedTableName 限定表名
     * @return 表容量查询SQL，如果不支持则返回null
     */
    protected String getTableCapacitySQLQuery(String qualifiedTableName) {
        // 默认实现返回null，表示不支持表容量查询
        // 子类可以重写此方法提供具体的实现
        return null;
    }

    protected long countRows(String qualifiedTableName) throws InitializationException {
        long rowCount = 0L;
        String countSql = null;
        try {
            countSql = getCountSQLQuery(qualifiedTableName);
        } catch (Exception e){
            LOG.error(e.getMessage(), e);
            return rowCount;
        }
        if (StringUtils.isBlank(countSql)){
            LOG.warn("countSql is null");
            return rowCount;
        }

        int sqlTimeoutSeconds = this.globalDataHolder.getTaskParam().getSampleSqlTimeout();
        LOG.debug("query count SQL - {}", countSql);
        try (Statement statement = getStatement();
             ResultSet resultSet = SQLDataPager.executeTimed(true, statement, countSql, sqlTimeoutSeconds)) {
            LOG.debug("Done Executing sql - {}", countSql);
            if (resultSet.next()) {
                rowCount = resultSet.getLong(1);
            }
            return rowCount;
        } catch (SQLException exception) {
            exception.printStackTrace();
            String message = Messages.getString("Could not get a row count for: '${tablename}' on '${datasourceUrl}'", "tablename", String.valueOf(qualifiedTableName), "datasourceUrl", getDatasource().getInstanceName());
            LOG.warn("{} running query is:{}", message, countSql);
            throw new InitializationException(message, countSql, exception);
        } catch (TimeoutException e) {
            LOG.error(e.getMessage(), e);
        }
        return rowCount;
    }

    public long getRowCount() {
        if (this.getCurrentTable().getRowCount() == 0L) {
            return this.countRows();
        }
        return this.getCurrentTable().getRowCount();
    }

    public long countRows() {
        String qualifiedTableName = this.getQualifiedName();
        try {
            return this.countRows(qualifiedTableName);
        } catch (InitializationException var5) {
            try {
                if (this.con == null) {
                    ++this.conClose;
                } else {
                    if (this.con.isClosed()) {
                        ++this.conClose;
                    }
                }
            } catch (Exception ignored) {
            }
            return -1L;
        }
    }

    /**
     * 获取表容量大小（字节）
     *
     * @param qualifiedTableName 限定表名
     * @return 表容量大小，如果获取失败或不支持则返回0
     * @throws InitializationException 初始化异常
     */
    protected long getTableCapacity(String qualifiedTableName) throws InitializationException {
        long tableCapacity = 0L;
        String capacitySql = null;
        try {
            capacitySql = getTableCapacitySQLQuery(qualifiedTableName);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            return tableCapacity;
        }

        if (StringUtils.isBlank(capacitySql)) {
            LOG.debug("Table capacity SQL is null, capacity query not supported for this database");
            return tableCapacity;
        }

        int sqlTimeoutSeconds = this.globalDataHolder.getTaskParam().getSampleSqlTimeout();
        LOG.debug("query table capacity SQL - {}", capacitySql);
        try (Statement statement = getStatement();
             ResultSet resultSet = SQLDataPager.executeTimed(true, statement, capacitySql, sqlTimeoutSeconds)) {
            LOG.debug("Done Executing table capacity sql - {}", capacitySql);
            if (resultSet.next()) {
                tableCapacity = resultSet.getLong(1);
            }
            return tableCapacity;
        } catch (SQLException exception) {
            exception.printStackTrace();
            String message = Messages.getString("Could not get table capacity for: '${tablename}' on '${datasourceUrl}'", "tablename", String.valueOf(qualifiedTableName), "datasourceUrl", getDatasource().getInstanceName());
            LOG.warn("{} running query is:{}", message, capacitySql);
            // 对于表容量获取失败，我们不抛出异常，而是返回0，让程序继续执行
            return 0L;
        } catch (TimeoutException e) {
            LOG.error(e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 获取当前表的容量大小
     *
     * @return 表容量大小，如果获取失败或不支持则返回0
     */
    public long getTableCapacity() {
        String qualifiedTableName = this.getQualifiedName();
        try {
            return this.getTableCapacity(qualifiedTableName);
        } catch (InitializationException var5) {
            try {
                if (this.con == null) {
                    ++this.conClose;
                } else {
                    if (this.con.isClosed()) {
                        ++this.conClose;
                    }
                }
            } catch (Exception ignored) {
            }
            return 0L;
        }
    }

    /**
     * 将传入的 name 使用引用字符包起来，例如 `tableName`
     *
     * @param name 可能的值 catalog/schema/tableName/columnName
     * @return
     */
    public String quoteIdentifiers(String name) {
        if (name != null && name.length() > 0) {
            String quote = this.getQuote();
            if (quote != null && quote.length() > 0) {
                return quote + name + quote;
            }
        }
        return name;
    }

    public String getConnectionDescriptor() {
        return this.getDatasource().getConnectionDescriptor();
    }

    public void release() {
        if (this.tableColumns != null) {
            this.tableColumns.clear();
        }

        this.tableColumnsTable = null;
        if (this.primaryKeyColumns != null) {
            this.primaryKeyColumns.clear();
        }

        if (this.tables != null) {
            this.tables.clear();
        }

        if (this.catalogs != null) {
            this.catalogs.clear();
        }

        this.closeConnection();
        if (this.conOpen > this.conClose) {
            String message = Messages.getString("Orphaned connections for: '${datasourceName}' Opened: ${opened} Closed: ${closed}.  Please contact dcap Support.", "datasourceName", String.valueOf(this.getDatasource()), "opened", String.valueOf(this.conOpen), "closed", String.valueOf(this.conClose));
            LOG.error(message + System.lineSeparator() + RuleEvaluator.arrayToString(Thread.currentThread().getStackTrace(), System.lineSeparator()));
        }
        this.datasource.release();
        this.datasource = null;
        List<RuleEvaluator> ruleEvaluatorList = this.globalDataHolder.getRuleEvaluatorList();
        for (RuleEvaluator ruleEvaluator : ruleEvaluatorList) {
            if (ruleEvaluator instanceof ColumnJniComplexRuleEvaluator){
                GuardClassifierJniComplexRule ruleDefinition = (GuardClassifierJniComplexRule) ruleEvaluator.getRuleDefinition();
                ruleDefinition.releaseClassifier();
            }
        }
    }

    public TaskGlobalDataHolder getTaskGlobalDataHolder() {
        return globalDataHolder;
    }

    public String getInventoryDbName() {
        String inventoryDbName = this.instanceName;
        if (StringUtils.isBlank(inventoryDbName)) {
            inventoryDbName = this.getDatasource().getInventoryDbName();
        }
        return inventoryDbName;
    }

    public Map<String,String> evaluateRules() throws ClassificationError, DBDriverException, ClassifierException {
        Long tenantId = this.getDatasource().getTenantId();
        String dataSourceId = this.getDatasource().getId();
        Clock clock = new Clock();
        int tableCount = 0;
        RateLimiter rateLimiter = RateLimiter.create(globalDataHolder.getTaskParam().getPermitsPerSecond());
        Map<String,String> tablesResult = new HashMap<>();
        
        while (this.hasMoreTables()) {
            if (this.getTaskGlobalDataHolder().getProbeClientTaskContext().checkInterrupt()) {
                LOG.debug("Scan task is interrupted in run rule");
                this.globalDataHolder.getProbeClientTaskContext()
                        .reportInterrupted(StatusRecord.Position.RunRule, "Scan task is interrupted in run rule")
                        .sendToServer();
                break;
            }

            ContextTable contextTable = this.getNextTable();
            if (contextTable == null) {
                break;
            }


            tableCount++;
            if (tableCount % 40 == 0){
                this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_TABLE_COUNT, 0).sendToServer();
            }

            String identityName = contextTable.getIdentityName();
            // 无论如何都会保存本次扫描到的表信息，因为后续扫描会依靠这份结果进行比较。
            tablesResult.put(identityName, identityName);

            String hash = this.globalDataHolder.getHashTables().remove(identityName);
            // 如果开启增量识别，只有新表才会识别。
            if (this.globalDataHolder.getTaskParam().getIncrementalEvaluationEnabled()){
                if (org.apache.commons.lang3.StringUtils.isNotBlank(hash)){// todo 应该是不为空且比对 hash 相同才跳过。
                    LOG.warn("Because incremental evaluation is turned on, the table [{}] has already been evaluated in the historical scan task and the table will be skipped.", identityName);
                    continue;
                }
            }

            // 使用智能分析模块尝试复用结果
            Map<String, List<MatchedResult>> cachedResults = null;
            for (IntelligentAnalyzer analyzer : analyzers) {
                cachedResults = analyzer.analyze(this, contextTable);
                if (cachedResults != null) {
                    LOG.info("Using cached recognition results for table: {}", contextTable.getTableName());
                    break;
                }
            }

            // 在处理请求之前先获取一个许可
            rateLimiter.acquire(); // 这会阻塞直到获得许可

            // 添加行数
            Long tableRowCount = null;
            boolean isTableRowCountEnabled = this.getTaskGlobalDataHolder().getTaskParam().isTableRowCountEnabled();
            if (isTableRowCountEnabled) {
                tableRowCount = this.getRowCount();
            }
            // 表容量大小
            Long tableCapacity = null;
            boolean isTableCapacityEnabled = this.getTaskGlobalDataHolder().getTaskParam().isTableCapacityEnabled();
            if (isTableCapacityEnabled) {
                tableCapacity = this.getTableCapacity();
            }


            clock.incrementCount();
            String logMsg = "当前扫描表名称: "+contextTable.getTableName()+", " +
                    "记录行数: "+(isTableRowCountEnabled? tableRowCount:"未开启") +"; "+
                    "容量: "+(isTableCapacityEnabled? tableCapacity:"未开启") +"; "+
                    "表类型: "+contextTable.getTableTypeName()+"; table count:"+clock.getCount()+"; ";
            LOG.info(clock.getElapsedMessage(logMsg));
            if (cachedResults != null) {
                // 使用缓存结果
                recordInventory(tableRowCount, tableCapacity, tenantId, dataSourceId, cachedResults);
                continue;
            }

            final Map<String, List<MatchedResult>> matchedResultMap = new HashMap<>();

            List<RuleEvaluator> ruleEvaluatorList = this.globalDataHolder.getRuleEvaluatorList();
            LOG.debug("execute rule evaluator for table: {}, rule size: {}", contextTable.getTableName(), ruleEvaluatorList.size());
            Map<String, List<RuleEvaluator>> classifiedEvaluatorMap = ruleEvaluatorList.stream().collect(Collectors.groupingBy(RuleEvaluator::getExprType));
            RuleResults ruleResults = null;

            List<RuleEvaluator> columnNameList = classifiedEvaluatorMap.get("catalog");
            if (columnNameList != null && !columnNameList.isEmpty()) {
                Clock executeColumnNameClock = new Clock();
                for (RuleEvaluator ruleEvaluator : columnNameList) {
                    if (this.globalDataHolder.getProbeClientTaskContext().checkInterrupt()) {
                        break;
                    }
                    SearchParameters searchParameters = ruleResults != null ? ruleResults.getNextSearchParameters() :
                            new SearchParameters(this);
                    ruleResults = ruleEvaluator.invokeRule(this, searchParameters);
                    if (!ruleResults.wasMatch()) {
                        continue;
                    }

                    LOG.debug(this.getCurrentTable().getTableName() + " with columns ( column ): " + ruleResults.getMatchedColumnNames());

                    List<String> matchedColumnNames = ruleResults.getMatchedColumnNames();
                    for (String matchedColumnName : matchedColumnNames) {
                        List<MatchedResult> rules = matchedResultMap.computeIfAbsent(matchedColumnName.toLowerCase(), k -> new ArrayList<>());
                        rules.add(new MatchedResult(ruleEvaluator.getTranslatedName(), ruleEvaluator.getDataTagTypeForDspm(), MatchedResult.CONFIRMED, 100));
                    }

                    // 如果规则已经满足。就不再继续了。
                    if (!ruleEvaluator.getContinueOption().continueToNextRule(ruleResults.wasMatch())) {
                        break;
                    }
                }
                String msg = "execute column name rule for table: "+contextTable.getTableName()+", rule size: "+columnNameList.size()+"; ";
                LOG.info(executeColumnNameClock.getElapsedMessage(msg));
            }

            List<RuleEvaluator> columnDataList = classifiedEvaluatorMap.get("data");
            if (columnDataList != null && !columnDataList.isEmpty()) {
                Clock executeColumnDataClock = new Clock();
                for (RuleEvaluator ruleEvaluator : columnDataList) {
                    if (this.globalDataHolder.getProbeClientTaskContext().checkInterrupt()) {
                        break;
                    }
                    SearchParameters searchParameters = ruleResults != null ? ruleResults.getNextSearchParameters() : new SearchParameters(this);
                    ruleResults = ruleEvaluator.invokeRule(this, searchParameters);
                    if (!ruleResults.wasMatch()) {
                        continue;
                    }


                    LOG.debug(this.getCurrentTable().getTableName() + " with columns (data): " + ruleResults.getMatchedColumnNames());

                    List<String> matchedColumnNames = ruleResults.getMatchedColumnNames();
                    for (String matchedColumnName : matchedColumnNames) {
                        List<MatchedResult> rules = matchedResultMap
                                .computeIfAbsent(matchedColumnName.toLowerCase(), k -> new ArrayList<>());
                        rules.add(new MatchedResult(ruleEvaluator.getTranslatedName(), ruleEvaluator.getDataTagTypeForDspm(), MatchedResult.CONFIRMED, 100));
                    }

                    // 如果规则已经满足。就不再继续了。
                    if (!ruleEvaluator.getContinueOption().continueToNextRule(ruleResults.wasMatch())) {
                        break;
                    }
                }
                String msg = "execute column data rule for table: "+contextTable.getTableName()+", rule size: "+columnDataList.size()+"; ";
                LOG.info(executeColumnDataClock.getElapsedMessage(msg));
            }

            List<RuleEvaluator> complexRuleList = classifiedEvaluatorMap.get("rule");
            if (complexRuleList != null && !complexRuleList.isEmpty()) {

                Clock executeColumnRuleClock = new Clock();
                complexRuleList.sort(null);
                for (RuleEvaluator ruleEvaluator : complexRuleList) {
                    if (this.globalDataHolder.getProbeClientTaskContext().checkInterrupt()) {
                        break;
                    }
                    SearchParameters searchParameters = ruleResults != null ? ruleResults.getNextSearchParameters() :
                            new SearchParameters(this);
                    // 设置 matchedResultMap 目的是为了执行模型时，可以使用之前列匹配的结果。
                    searchParameters.setMatchedResultMap(matchedResultMap);
                    LOG.debug("start execute rule model; table name: {}, rule model name: {}, translatedName: {}",
                            contextTable.getTableName(), ruleEvaluator.getName(), ruleEvaluator.getTranslatedName());
                    ruleResults = ruleEvaluator.invokeRule(this, searchParameters);
                    if (!ruleResults.wasMatch()) {
                        continue;
                    }

                    LOG.debug("execute rule model results; tableName: {},  rule model name: {}, translatedName: {}, with columns (rule): {}",
                            this.getCurrentTable().getTableName(), ruleEvaluator.getName(), ruleEvaluator.getTranslatedName(),
                            ruleResults.getMatchedColumnNames());

                    // 将列的匹配结果添加到 matchedRules 这个 map 中，key 就是 columnName，值为匹配的标签列表
                    List<Pair<ContextColumn, MatchedResult>> matchedColumnsResult = ruleResults.getMatchedColumnsResult();
                    for (Pair<ContextColumn, MatchedResult> contextColumnMapPair : matchedColumnsResult) {
                        ContextColumn matchedColumn = contextColumnMapPair.getKey();
                        MatchedResult matchedResult = contextColumnMapPair.getValue();
                        List<MatchedResult> rules = matchedResultMap.computeIfAbsent(matchedColumn.getColumnName().toLowerCase(),
                                k -> new ArrayList<>());
                        rules.add(matchedResult);
                    }

                    // 如果规则已经满足。就不再继续了。
                    if (!ruleEvaluator.getContinueOption().continueToNextRule(ruleResults.wasMatch())) {
                        break;
                    }
                }
                String msg = "execute rule model for table: "+contextTable.getTableName()+", rule model size: "+complexRuleList.size()+"; ";
                LOG.info(executeColumnRuleClock.getElapsedMessage(msg));
            }

            // 缓存识别结果
            for (IntelligentAnalyzer analyzer : analyzers) {
                analyzer.cacheResults(this, contextTable, matchedResultMap);
            }

            // 每一张表记录一次 inventory
            this.recordInventory(tableRowCount, tableCapacity, tenantId, dataSourceId, matchedResultMap);
            this.cleanupSamples();
        }
        // 清理识别结果的缓存。
        RecognitionResultCache.getInstance().clearAll();
        return tablesResult;
    }

    public void recordInventory(Long tableRowCount, Long tableCapacity, Long tenantId, String dataSourceId,
                                   Map<String, List<MatchedResult>> matchedResultMap) throws InitializationException {
        Long currentVersion = this.getTaskGlobalDataHolder().getCurrentVersion();
        String inventoryDbName = getInventoryDbName();
        String rowCount = tableRowCount == null ? "0" : String.valueOf(tableRowCount);
        String rowCountLimit = this.getTaskGlobalDataHolder().getTaskParam().isTableRowCountEnabled() ? String.valueOf(this.getTaskGlobalDataHolder().getTaskParam().getTableRowCountLimit()) : "0";
        String capacity = tableCapacity == null ? "0" : String.valueOf(tableCapacity);
        List<ContextColumn> tableColumns = this.getTableColumns();
        TaskReport taskReport = this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_COLUMN_COUNT, tableColumns.size());
        int columnsCount = Integer.parseInt(Objects.toString(taskReport.get(ScanDbReport.FIELD_COLUMN_COUNT), "-1"));
        if (columnsCount % 256 == 0){
            taskReport.sendToServer();
        }
        List<String> columns = new ArrayList<>();

        for (ContextColumn column : tableColumns) {
            String qualName = inventoryDbName == null ? column.getIdentityName() : inventoryDbName + "." + column.getIdentityName();

            String database = inventoryDbName == null? column.getTable().getCatalog(): inventoryDbName;
            String schema = column.getTable().getSchema();
            if (StringUtils.isBlank(database)){
                database = schema;
                schema = null;
            }
            String tableName = column.getTable().getTableName();
            String columnName = column.getColumnName();
            boolean isPrimaryKey = column.isPrimaryKey();
            String columnComment = column.getRemarks();
            int columnPosition = column.getPosition();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(columnComment)) {
                columnComment = columnComment.replaceAll(",", " ");
            }
            ContextColumn.ColumnType columnType = column.getColumnType();
            String actualColumnTypeName = column.getTypename();
            String columnTypeCategoryName = columnType.getTypeName();
            int columnTypeSize = column.getSize();
            int columnScale = column.getScale();
            List<MatchedResult> resultList = matchedResultMap.getOrDefault(columnName.toLowerCase(), Collections.emptyList());
            Set<String> dataTagsHashSet = new HashSet<>();
            Map<String, String> matchingResultMap = new HashMap<>();
            for (MatchedResult matchedResult : resultList) {
                String dataTag = matchedResult.getDataTag();
                if (dataTag == null) {
                    continue;
                }
                dataTagsHashSet.add(dataTag);
                matchingResultMap.put(dataTag, dataTag.concat(MATCHING_RESULT_DELIMITER).concat(String.valueOf(matchedResult.getScore()))
                        .concat(MATCHING_RESULT_DELIMITER)
                        .concat(matchedResult.getEvaluation()).concat(MATCHING_RESULT_DELIMITER)
                        .concat(matchedResult.getDataTagType())
                );
            }


            Map<String, Set<String>> dataMarkings = this.getTaskGlobalDataHolder().getDataMarkings();
            if (dataMarkings != null) {
                String manualTagsKey = tenantId + "_" + dataSourceId + "_" + qualName.toLowerCase() + "_ManualTags";
                String deletedAutoTagsKey = tenantId + "_" + dataSourceId + "_" + qualName.toLowerCase() + "_DeletedAutoTags";
                Set<String> manualTagsSet = dataMarkings.get(manualTagsKey);
                Set<String> deletedAutoTagsSet = dataMarkings.get(deletedAutoTagsKey);
                if (deletedAutoTagsSet != null) {
                    dataTagsHashSet.removeAll(deletedAutoTagsSet);
                    for (String deletedAutoTag : deletedAutoTagsSet) {
                        matchingResultMap.remove(deletedAutoTag);
                    }
                }
                // 手动打标要添加
                if (manualTagsSet != null) {
                    dataTagsHashSet.addAll(manualTagsSet);
                    for (String manualTag : manualTagsSet) {
                        // 暂时手动打标，只允许 COMPOUND 复合类型。所以这里可以确定的直接 添加 COMPOUND
                        matchingResultMap.put(manualTag, manualTag.concat(MATCHING_RESULT_DELIMITER)
                                .concat("100").concat(MATCHING_RESULT_DELIMITER).concat("CONFIRMED")
                                .concat(MATCHING_RESULT_DELIMITER).concat("COMPOUND")
                        );
                    }
                }
            }

            String dataTags = StringUtils.join(dataTagsHashSet, ";");
            String dataTagAndScoreResult = StringUtils.join(matchingResultMap.values(), ";");
            try {
                String tableTypeName = null;
                String tableComment = null;
                ContextTable table = column.getTable();
                long tableCreatedTimestamp = table.getTableCreatedTimestamp();
                // 记录 table type 时，使用宽泛的 table type 来记录，比如物化视图，就统一也是 view
                tableTypeName = table.getType().getTypeName();
                if ("SYSTEM TABLE".equals(tableTypeName)){
                    tableTypeName = "TABLE";
                }
                tableComment = table.getTableComment();
                if (tableComment != null) {
                    tableComment = tableComment.replaceAll("\r\n", " ");
                    tableComment = tableComment.replaceAll("\r", " ");
                    tableComment = tableComment.replaceAll("\n", " ");
                }
                if (columnComment != null) {
                    columnComment = columnComment.replaceAll("\r\n", " ");
                    columnComment = columnComment.replaceAll("\n", " ");
                    columnComment = columnComment.replaceAll("\r", " ");
                }
//                System.out.println("tableName: "+table.getTableName()+", columnName: "+columnName+", equivalents: "+columnType.getEquivalents()+", typeName: "+columnType.getTypeName()+", typeSize: "+columnTypeSize);
                // datasource_id,qual_name,data_tags,update_time,tenant_id, tableTypeName, column_comment, data_tag_type,columnTypeName,columnTypeSize
                if (!scanResultRecordAsTable){
                    String[] entries = {
                            dataSourceId, qualName, dataTags,
                            String.valueOf(System.currentTimeMillis()),
                            String.valueOf(tenantId), "Scan",
                            tableTypeName, columnComment,
                            columnTypeCategoryName, String.valueOf(columnTypeSize),
                            String.valueOf(columnPosition), rowCount, rowCountLimit,
                            dataTagAndScoreResult, tableComment,
                            String.valueOf(this.getTaskGlobalDataHolder().getScanJobHistoryId()),
                            String.valueOf(currentVersion),
                            String.valueOf(tableCreatedTimestamp), database, schema, tableName, columnName, String.valueOf(isPrimaryKey),
                            capacity
                    };
                    this.globalDataHolder.getInventoryWriter().writeEntries(entries);
                } else {
                    columns.add(JSON.from(UtilMisc.toMap(
                            "dataSourceId", dataSourceId,
                            "databaseInstanceName", this.getDatasource().getName(),
                            "qualName", qualName,
                            "dataTags", dataTags,
                            "updateTime", System.currentTimeMillis(),
                            "tenantId", tenantId,
                            "tableTypeName", tableTypeName,
                            "columnComment", columnComment,
                            "columnTypeCategoryName", columnTypeCategoryName,
                            "actualColumnTypeName", actualColumnTypeName,
                            "columnTypeSize", columnTypeSize,
                            "columnScale", columnScale,
                            "columnPosition", columnPosition,
                            "rowCount", rowCount,
                            "rowCountLimit", rowCountLimit,
                            "dataTagAndScoreResult", dataTagAndScoreResult,
                            "tableComment", tableComment,
                            "scanJobHistoryId", this.getTaskGlobalDataHolder().getScanJobHistoryId(),
                            "currentVersion", currentVersion,
                            "tableCreatedTimestamp", tableCreatedTimestamp,
                            "database", database,
                            "schema", schema,
                            "tableName", tableName,
                            "columnName", columnName,
                            "primaryKey", isPrimaryKey,
                            "tableCapacity", capacity
                    )).toString());
                }
            } catch (Exception e) {
                LOG.error("Failed to write the inventory csv file", e);
                e.printStackTrace();
            }
        }
        try {
            if (scanResultRecordAsTable){
                this.globalDataHolder.getInventoryWriter().writeEntries(columns.toArray(new String[0]));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取指定catalog下所有表的注释映射
     * 默认返回空映射，子类可以重写此方法提供数据库特定的表注释获取逻辑
     * 
     * @param catalog 数据库catalog名称
     * @return Map<String, String> key为表全名(catalog.tableName)，value为表注释
     */
    protected Map<String, String> getTableComments(String catalog) {
        return new HashMap<>();
    }

    public List<DatabaseUser> findDatabaseUsers(){
        return null;
    }
    public List<DbPrivilege> retrievePrivileges(){
        return null;
    }

//    public void recordUsersAndPrivileges() throws ClassificationError, DBDriverException, IOException {
//        List<DatabaseUser> databaseUsers = this.findDatabaseUsers();
//        List<DbPrivilege> dbPrivilegeList = this.retrievePrivileges();
//        Long tenantId = this.getDatasource().getTenantId();
//        String dataSourceId = this.getDatasource().getId();
//        Long currentVersion = this.getTaskGlobalDataHolder().getCurrentVersion();
//        if (databaseUsers != null){
//            for (DatabaseUser databaseUser : databaseUsers) {
//                String[] entries = {
//                        dataSourceId, String.valueOf(tenantId),
//                        String.valueOf(this.getTaskGlobalDataHolder().getScanJobHistoryId()),
//                        String.valueOf(currentVersion), databaseUser.getUsername(),
//                        Base64Util.Encrypt(JSON.from(databaseUser.getAttributes()).toString())
//                };
//                this.globalDataHolder.getDatabaseUsersWriter().writeEntries(entries);
//            }
//        }
//
//        if (dbPrivilegeList != null) {
//            for (DbPrivilege dbPrivilege : dbPrivilegeList) {
//                String[] entries = {
//                        dataSourceId, String.valueOf(tenantId),
//                        String.valueOf(this.getTaskGlobalDataHolder().getScanJobHistoryId()),
//                        String.valueOf(currentVersion), dbPrivilege.getTableCatalog(), dbPrivilege.getTableSchema(),
//                        dbPrivilege.getDatabaseObjectType(), dbPrivilege.getDatabaseObjectName(),
//                        dbPrivilege.getDatabaseObjectFullName(), dbPrivilege.getPrivilegeName(),
//                        dbPrivilege.getGrantor(), dbPrivilege.getGrantee(), dbPrivilege.getIsGrantable()
//                };
//                this.globalDataHolder.getDbPrivilegesWriter().writeEntries(entries);
//            }
//            Map<String, DbPrivilege> dbPrivilegeMap = new HashMap<>();
//            for (DbPrivilege dbPrivilege : dbPrivilegeList) {
//                if (Objects.equals(dbPrivilege.getDatabaseObjectType(),"COLUMN")) {
//                    continue;
//                }
//                String key = org.apache.commons.lang3.StringUtils.joinWith(".",
//                        dbPrivilege.getGrantor(), dbPrivilege.getGrantee(), dbPrivilege.getDatabaseObjectFullName());
//                DbPrivilege privilege = dbPrivilegeMap.get(key);
//                if (privilege == null){
//                    dbPrivilegeMap.put(key, dbPrivilege);
//                } else {
//                    privilege.setPrivilegeName(org.apache.commons.lang3.StringUtils.joinWith(
//                            ",", privilege.getPrivilegeName(), dbPrivilege.getPrivilegeName()
//                    ));
//                }
//            }
//            //  identity, permission, asset, granted_by
//            for (Map.Entry<String, DbPrivilege> dbPrivilegeEntry : dbPrivilegeMap.entrySet()) {
//                DbPrivilege dbPrivilege = dbPrivilegeEntry.getValue();
//                String grantor = dbPrivilege.getGrantor();
//                String[] entries = {
//                        dataSourceId, String.valueOf(tenantId),
//                        String.valueOf(this.getTaskGlobalDataHolder().getScanJobHistoryId()),
//                        String.valueOf(currentVersion), grantor, "FULL",
//                        dbPrivilege.getDatabaseObjectFullName(), grantor + " provides permissions ['OWNERSHIP']"
//                };
//                this.globalDataHolder.getDbPrivilegesReportWriter().writeEntries(entries);
//
//                String privileges = org.apache.commons.lang3.StringUtils.joinWith("','",
//                        dbPrivilege.getPrivilegeName().split(",")
//                );
//                String grantee = dbPrivilege.getGrantee();
//                int maxLevel = Arrays.stream(dbPrivilege.getPrivilegeName().split(","))
//                        .filter(Objects::nonNull)
//                        .map(PermissionLevel::map)
//                        .filter(Objects::nonNull)
//                        .map(PermissionLevel::getLevel)
//                        .filter(Objects::nonNull)
//                        .reduce(Math::max).orElse(0);
//                String maxPrivilege = PermissionLevel.valueOf(maxLevel).name();
//                entries = new String[]{
//                        dataSourceId, String.valueOf(tenantId),
//                        String.valueOf(this.getTaskGlobalDataHolder().getScanJobHistoryId()),
//                        String.valueOf(currentVersion), grantee, maxPrivilege,
//                        dbPrivilege.getDatabaseObjectFullName(), grantor + " provides permissions ['"+privileges+"']"
//                };
//                this.globalDataHolder.getDbPrivilegesReportWriter().writeEntries(entries);
//            }
//        }
//    }


}
