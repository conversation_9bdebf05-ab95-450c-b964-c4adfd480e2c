package com.dcap.classifier.context;

import com.dcap.classifier.Clock;
import com.dcap.classifier.InitializationException;
import com.dcap.classifier.RuleEvaluator;
import com.dcap.datalayer.ClassifierDataSource;
import com.dcap.datalayer.ConnectionException;
import com.dcap.datalayer.TaskGlobalDataHolder;
import com.dcap.utils.Messages;
import com.yd.dcap.classifier.OracleDatasource;
import com.yd.dcap.classifier.taskreport.ScanDbReport;
import com.yd.dcap.classifier.taskreport.StatusRecord;
import com.yd.dcap.probe.client.TaskConfig;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OracleRuleContext extends RuleContext {

    private static final transient String[] TBL_AND_VIEW = new String[]{"TABLE", "VIEW"};
    private static final transient String SYN_OWNER = "SN_OWN";
    private static final transient String SYN_NAME = "SN_NM";
    private static final transient String REF_OWNER = "RF_OWN";
    private static final transient String REF_NAME = "RF_NM";
    private static final transient String REF_TYPE = "RF_TYP";

    private static final transient String REF_COMMENT = "RF_CMM";

    private static final transient String EMPTY_CATALOG_QUERY_8 = "select u.username from all_users u , all_objects o where u.username = o.owner (+) group by u.username having count(object_name) = 0 order by u.username";
    private static final transient String EMPTY_CATALOG_QUERY_8_PLUS = "select u.username from all_users u left outer join all_objects o on u.username = o.owner group by u.username having count(object_name) = 0 order by u.username";
    private static final transient String SQL_TBL_SYNONYM_CONTAIN_VIEW =
            "select SYN.OWNER as SN_OWN, SYN.SYNONYM_NAME as SN_NM, SYN.TABLE_OWNER as RF_OWN, SYN.TABLE_NAME as RF_NM, " +
                    "OBJ.OBJECT_TYPE as RF_TYP " +
            "from ALL_OBJECTS OBJ " +
            "JOIN ALL_SYNONYMS SYN ON OBJ.OBJECT_NAME=SYN.TABLE_NAME AND OBJ.OWNER=SYN.TABLE_OWNER  " +
            "where trim(both from SYN.DB_LINK) is null " +
            "and ( OBJ.OBJECT_TYPE = 'TABLE' or OBJ.OBJECT_TYPE = 'VIEW' or OBJ.OBJECT_TYPE = 'SYNONYM' ) " +
            "and SYN.TABLE_OWNER = ? order by OBJ.OBJECT_TYPE, SYN.OWNER, SYN.SYNONYM_NAME";

    private static final transient String SQL_TBL_SYNONYM =
            "select SYN.OWNER as SN_OWN, SYN.SYNONYM_NAME as SN_NM, SYN.TABLE_OWNER as RF_OWN, SYN.TABLE_NAME as RF_NM, " +
                    "OBJ.OBJECT_TYPE as RF_TYP " +
                    "from ALL_OBJECTS OBJ " +
                    "JOIN ALL_SYNONYMS SYN ON OBJ.OBJECT_NAME=SYN.TABLE_NAME AND OBJ.OWNER=SYN.TABLE_OWNER  " +
                    "where trim(both from SYN.DB_LINK) is null " +
                    "and ( OBJ.OBJECT_TYPE = 'TABLE' or OBJ.OBJECT_TYPE = 'SYNONYM' ) " +
                    "and SYN.TABLE_OWNER = ? order by OBJ.OBJECT_TYPE, SYN.OWNER, SYN.SYNONYM_NAME";
    private static final transient String SQL_SYNONYM_REF =
            "select OBJ.OWNER as RF_OWN, OBJ.OBJECT_NAME as RF_NM, OBJ.OBJECT_TYPE as RF_TYP " +
            "from ALL_OBJECTS OBJ JOIN ALL_SYNONYMS SYN ON OBJ.OBJECT_NAME=SYN.TABLE_NAME AND OBJ.OWNER=SYN.TABLE_OWNER  " +
            "where trim(both from SYN.DB_LINK) is null " +
            "and SYN.OWNER = ? and SYN.SYNONYM_NAME = ?";
    private static final transient String COL_NAME = "CL_NM";
    private static final transient String COL_TYPE_NAME = "CL_TYP_NM";
    private static final transient String COL_SIZE = "CL_SZ";
    private static final transient String COL_PRECISION = "CL_DP";
    private static final transient String COL_SCALE = "CL_DS";

    private static final transient String COL_COMMENT = "CL_CM";

    private static final transient String COL_POSITION = "CL_ID";
    private static final transient String SQL_TABLE_COLUMN = "SELECT COL.COLUMN_NAME as CL_NM, COL.DATA_TYPE as CL_TYP_NM, " +
            "COL.DATA_LENGTH as CL_SZ, COM.COMMENTS CL_CM, COL.COLUMN_ID CL_ID, COL.DATA_PRECISION CL_DP, COL.DATA_SCALE CL_DS " +
            "FROM ALL_TAB_COLUMNS COL LEFT JOIN ALL_COL_COMMENTS COM " +
            "ON COL.COLUMN_NAME = COM.COLUMN_NAME AND COL.TABLE_NAME = COM.TABLE_NAME AND COL.OWNER = COM.OWNER " +
            "where COL.OWNER = ? and COL.TABLE_NAME = ?";
    private static final transient String PRV_GRANTOR = "PV_GRTOR";
    private static final transient String PRV_GRANTEE = "PV_GRTEE";
    private static final transient String PRV_GRANT = "PV_GRT";
    private static final transient String PRV_GRANTABLE = "PV_GRTBL";
    private static final transient String SQL_TABLE_PRIVILEGE = "select PRV.GRANTOR as PV_GRTOR, PRV.GRANTEE as PV_GRTEE, PRV.PRIVILEGE as PV_GRT, PRV.GRANTABLE as PV_GRTBL from  DBA_TAB_PRIVS PRV where PRV.OWNER = ? and PRV.TABLE_NAME = ?";
    private static final transient String PK_ORDINAL = "PK_ORD";
    private static final transient String SQL_TABLE_PK = "select\tCOL.COLUMN_NAME as CL_NM, COL.POSITION as PK_ORD from ALL_CONS_COLUMNS COL, ALL_CONSTRAINTS CON where CON.CONSTRAINT_TYPE = 'P' and COL.CONSTRAINT_NAME = CON.CONSTRAINT_NAME and CON.OWNER = ? and CON.TABLE_NAME = ? order by COL.POSITION";
    private Boolean testForSynonyms;

    protected OracleRuleContext(ClassifierDataSource classifierDataSource, TaskGlobalDataHolder globalDataHolder) throws ConnectionException {
        super(classifierDataSource, globalDataHolder);
    }

    protected List<ContextTable> findCatalogTables(String catalog) {
        Clock clock = new Clock();
        LOG.info("获取所有的表：{}", catalog);
        this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_DATASOURCE_COUNT, 1);

        this.findCatalogTables(catalog, TBL_AND_VIEW);
        this.addTables(this.findTableSynonyms(catalog));

        clock.incrementCount(this.listCatalogTables().size());
        this.globalDataHolder.getProbeClientTaskContext().recordField(ScanDbReport.FIELD_TABLE_COUNT, clock.getCount()).sendToServer();
        LOG.info("{} {}", this.toString(), clock.getElapsedMessageForCounter("Total Tables for: '" + catalog + "'"));
        return this.listCatalogTables();
    }

    protected List<ContextTable> findCatalogTables(String catalog, String[] tableTypes) {
        Clock clock = new Clock();
        String catalogName = null;
        String schemaName = null;
        if (this.getDatasource().isCatalog()) {
            catalogName = catalog;
        } else {
            schemaName = catalog;
        }

        if (this.tables != null) {
            this.tables.clear();
        } else {
            this.tables = new ArrayList<>();
        }

        this.currentTable = null;
        // todo: 这里是调整一下传入的 tableTypes。
        tableTypes = this.getDatasource().getDriverInterface()
                .adjustTableTypes(tableTypes, true, this.getConnection());

        try {
            Set<String> selectedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
            Set<String> excludedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
            Set<String> selectedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedTable();
            Set<String> excludedTable = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedTable();
            Set<String> selectedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedView();
            Set<String> excludedView = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedView();
            String inventoryDbName = this.instanceName;
            if (StringUtils.isBlank(inventoryDbName)) {
                inventoryDbName = this.getDatasource().getInventoryDbName();
            }

            DatabaseMetaData metaData = this.getConnection().getMetaData();
            for (String currentTableType : tableTypes) {
                LOG.info("Get tables with catalog = {} and schema = {} and table type = {}", catalogName, schemaName, currentTableType);
                try (ResultSet resultSet = metaData.getTables(catalogName, schemaName, null, new String[]{currentTableType})){
                    LOG.info("Get tables - end");
                    while (resultSet.next()) {
                        String tableType = resultSet.getString("TABLE_TYPE");
                        String tableName = resultSet.getString("TABLE_NAME");
                        String tableCatalog = resultSet.getString("TABLE_CAT");
                        String tableSchema = resultSet.getString("TABLE_SCHEM");
                        String tableComment = resultSet.getString("REMARKS");

                        if (org.apache.commons.lang.StringUtils.isBlank(tableCatalog) && org.apache.commons.lang.StringUtils.isNotBlank(catalogName)) {
                            tableCatalog = catalogName;
                        }

                        // 当前表是属于内部 schema 或 内部 catalog 就跳过
                        if (isInternalSchema(this.getDatasource(), tableSchema) || isInternalCatalog(this.getDatasource(), tableCatalog)) {
                            LOG.debug("Catalog: " + catalog + ", Schema: " + tableSchema + " has been skipped ");
                            continue;
                        }
                        ContextTable table = this.createContextTable(tableCatalog, tableSchema, tableName, tableType, tableComment);
                        if (table == null) {
                            continue;
                        }
                        String tableIdentityName = table.getIdentityName().toLowerCase();
                        String schemaIdentityName = tableIdentityName.substring(0,tableIdentityName.indexOf(".")).toLowerCase();
                        tableIdentityName = inventoryDbName == null ? tableIdentityName : (inventoryDbName + "." + tableIdentityName).toLowerCase();
                        schemaIdentityName = inventoryDbName == null ? schemaIdentityName : (inventoryDbName + "." + schemaIdentityName).toLowerCase();
                        String tableTypeName = table.getTableTypeName();
                        if (excludedSchema.contains(schemaIdentityName)){
                            continue;
                        }
                        if ("TABLE".equals(tableTypeName) && excludedTable.contains(tableIdentityName)){
                            continue;
                        }
                        if ("VIEW".equals(tableTypeName) && excludedView.contains(tableIdentityName)){
                            continue;
                        }
                        if ((selectedSchema.isEmpty() || selectedSchema.contains(schemaIdentityName))
                                && (selectedTable.isEmpty() || selectedTable.contains(tableIdentityName))
                                && "TABLE".equals(tableTypeName)){
                            this.addTable(table);
                            clock.incrementCount();
                        } else if ((selectedSchema.isEmpty() || selectedSchema.contains(schemaIdentityName))
                                && (selectedView.isEmpty() || selectedView.contains(tableIdentityName))
                                && "VIEW".equals(tableTypeName)){
                            this.addTable(table);
                            clock.incrementCount();
                        }
                    }
                }
            }
            if (LOG.isInfoEnabled()) {
                String tableType = RuleEvaluator.arrayToString(tableTypes);
                LOG.debug(catalog + ": " + clock.getElapsedMessageForCounter(tableType));
            }
        } catch (SQLException sqlException) {
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'",
                    "datasourceName", String.valueOf(this.getDatasource().getDescriptor()),
                    "datasourceUrl", this.getConnectionDescriptor());
            LOG.error(message, sqlException);
            this.globalDataHolder.getProbeClientTaskContext()
                            .reportErrorOccurredExecuting(StatusRecord.Position.FindCatalogTables, "catalog [" + catalog + "]", null, sqlException);
        } catch (NullPointerException nullPointerException) {
            StringBuilder logMsg = new StringBuilder();
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource().getDescriptor()), "datasourceUrl", this.getConnectionDescriptor());
            logMsg.append(message);
            throw new DBDriverException(clock.getElapsedMessage(logMsg.toString()), nullPointerException);
        }
        // 根据表类型进行排序
        this.sortTables();
        return this.listCatalogTables();
    }

    /**
     * 查找表同义词
     * 同义词：就是定义一个字符串名称，用来代替一个有意义的引用对象。
     *
     * @param catalog
     * @return
     */
    protected List<ContextTable> findTableSynonyms(String catalog) {
        List<ContextTable> tables = new ArrayList<>();
        if (this.testForSynonyms == null) {
            // todo: testForSynonyms set true
            this.testForSynonyms = true;
        }
        String catalogName = null;
        String schemaName = null;
        if (this.getDatasource().isCatalog()) {
            catalogName = catalog;
        } else {
            schemaName = catalog;
        }

        if (!this.testForSynonyms) {
            return tables;
        }

        if (isInternalSchema(this.getDatasource(), catalog)) {
            LOG.debug("Schema {} has been skipped , no synonyms will be scanned for this schema", catalog);
            return tables;
        }
        Set<String> selectedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSchema();
        Set<String> excludedSchema = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSchema();
        Set<String> selectedSynonym = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getSelectedSynonym();
        Set<String> excludedSynonym = this.getTaskGlobalDataHolder().getTaskParam().getScanRange().getExcludedSynonym();
        String inventoryDbName = this.instanceName;
        if (StringUtils.isBlank(inventoryDbName)) {
            inventoryDbName = this.getDatasource().getInventoryDbName();
        }
        TaskConfig.TaskParam taskParam = this.getTaskGlobalDataHolder().getTaskParam();
        String tableType = taskParam.getTableType();
        try (PreparedStatement preparedStatement = tableType.contains("VIEW")?this.getPreparedStatement(SQL_TBL_SYNONYM_CONTAIN_VIEW):
                this.getPreparedStatement(SQL_TBL_SYNONYM)) {
            preparedStatement.setString(1, catalog);
            LOG.info("Get tables with catalog = {} and schema = {} and table type = Synonyms", catalogName, schemaName);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                LOG.info("Get tables - end");
                while (resultSet.next()) {
                    String synonymOwner = resultSet.getString(SYN_OWNER);
                    String synonymName = resultSet.getString(SYN_NAME);
                    String referencesOwner = resultSet.getString(REF_OWNER);
                    String referencesName = resultSet.getString(REF_NAME);
                    String referenceType = resultSet.getString(REF_TYPE);
                    ContextTable contextTable = this.createSynonymContextTable(synonymOwner, synonymName, referencesOwner,
                            referencesName, referenceType, "");
                    if (contextTable == null) {
                        continue;
                    }
                    String tableIdentityName = contextTable.getIdentityName().toLowerCase();
                    String schemaIdentityName = tableIdentityName.substring(0,tableIdentityName.lastIndexOf(".")).toLowerCase();
                    tableIdentityName = inventoryDbName == null ? tableIdentityName : (inventoryDbName + "." + tableIdentityName).toLowerCase();
                    schemaIdentityName = inventoryDbName == null ? schemaIdentityName : (inventoryDbName + "." + schemaIdentityName).toLowerCase();
                    if (excludedSchema.contains(schemaIdentityName) || excludedSynonym.contains(tableIdentityName)){
                        continue;
                    }

                    if ((selectedSchema.isEmpty() || selectedSchema.contains(schemaIdentityName))
                            && (selectedSynonym.isEmpty() || selectedSynonym.contains(tableIdentityName))){
                        tables.add(contextTable);
                    }
                }
            }
        } catch (SQLException sqlException) {
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource()), "datasourceUrl", this.getConnectionDescriptor());
            LOG.warn(message + System.lineSeparator() + sqlException);
        }
        return tables;
    }

    protected ContextTable createSynonymContextTable(String schema, String tableName, String referencesOwner,
                                                     String referencesName, String referenceType, String tableComment) {
        ContextTable synonym = this.findReference(referencesOwner, referencesName, referenceType, tableComment);
        if (synonym == null) {
            return null;
        }
        ContextTable table = this.createContextTable(schema, tableName, ContextTable.SYNONYM.getTypeName(), tableComment);
        table.setReference(synonym);
        return table;
    }

    protected ContextTable createContextTable(String schema, String tableName, String tableTypeName, String tableComment) {
        return this.createContextTable(null, schema, tableName, tableTypeName, tableComment);
    }

    protected ContextTable findReference(String referencesOwner, String referencesName, String tableType, String tableComment) {
        if (ContextTable.VIEW.isEquivalent(tableType) || ContextTable.TABLE.isEquivalent(tableType)) {
            return this.createContextTable(referencesOwner, referencesName, tableType, tableComment);
        }

        if (!ContextTable.SYNONYM.isEquivalent(tableType)) {
            LOG.debug("Not a valid synonym table reference type: {}", tableType);
            return null;
        }

        try (PreparedStatement preparedStatement = this.getPreparedStatement(SQL_SYNONYM_REF)) {
            preparedStatement.setString(1, referencesOwner);
            preparedStatement.setString(2, referencesName);

            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    String referenceOwner = resultSet.getString(REF_OWNER);
                    String referenceName = resultSet.getString(REF_NAME);
                    String referenceType = resultSet.getString(REF_TYPE);
//                    String referenceComment = resultSet.getString(REF_COMMENT);
                    return this.findReference(referenceOwner, referenceName, referenceType, "");
                }
            }
        } catch (SQLException sqlException) {
            String message = Messages.getString("Could not access table(s) for: '${datasourceName}' on: '${datasourceUrl}'", "datasourceName", String.valueOf(this.getDatasource()), "datasourceUrl", this.getConnectionDescriptor());
            LOG.warn(message + System.lineSeparator() + sqlException);
        }
        return null;
    }

    protected List<ContextColumn> findTableColumns() throws InitializationException {
        Clock clock = new Clock();
        ContextTable currentTable = this.getCurrentTable();

        ContextTable reference = currentTable;
        if (currentTable.getReference() != null) {
            reference = currentTable.getReference();
        }

        String schema = reference.getSchema();
        String tableName = reference.getTableName();
        Map<String, Boolean> primaryColumns = getPrimaryColumns();
        try (PreparedStatement preparedStatement = this.getPreparedStatement(SQL_TABLE_COLUMN)) {
            preparedStatement.setString(1, schema);
            preparedStatement.setString(2, tableName);
            preparedStatement.setQueryTimeout(this.getTaskGlobalDataHolder().getTaskParam().getSampleSqlTimeout());
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                List<ContextColumn> columns = new ArrayList<>();
                while (resultSet.next()) {
                    String columnName = resultSet.getString(COL_NAME);
                    String columnTypeName = resultSet.getString(COL_TYPE_NAME);
                    int columnSize = resultSet.getInt(COL_SIZE);
                    int columnPrecision = resultSet.getInt(COL_PRECISION);
                    if (columnTypeName != null && columnTypeName.contains("NUMBER") && columnPrecision > 0) {
                        columnSize = columnPrecision;
                    }
                    int columnScale = resultSet.getInt(COL_SCALE);
                    String columnComment = resultSet.getString(COL_COMMENT);
                    if(StringUtils.isNotBlank(columnTypeName)){
                        columnTypeName = columnTypeName.replaceAll("\\([0-9]+\\)","");
                    }
                    int columnPosition = resultSet.getInt(COL_POSITION);

                    ContextColumn column = this.createContextColumn(currentTable, columnName, columnTypeName, columnSize,
                            columnScale, columnComment,
                            columnPosition, primaryColumns.get(columnName) == Boolean.TRUE);
                    if (column != null) {
                        columns.add(column);
                        clock.incrementCount();
                    }
                }
                LOG.debug("Table: '{}' {}", currentTable, clock.getElapsedMessageForCounter("Columns"));
                return columns;
            }
        } catch (SQLException sqlException) {
            String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", tableName);
            LOG.error(message, sqlException);
            throw new InitializationException(clock.getElapsedMessage(message), sqlException.toString(), sqlException);
        }
    }

    protected Map<String,Boolean> getPrimaryColumns(){
        Map<String,Boolean> result = new HashMap<>();
        if (this.globalDataHolder.getDataSource().getEncryptionSwitch() == null ||
                this.globalDataHolder.getDataSource().getEncryptionSwitch() == 0){
            return result;
        }

        ContextTable currentTable = this.getCurrentTable();
        ContextTable dereferenceTable = this.dereference(currentTable);
        if (dereferenceTable.isOfType(ContextTable.TABLE)) {
            try (PreparedStatement preparedStatement = this.getPreparedStatement(SQL_TABLE_PK)) {
                preparedStatement.setString(1, dereferenceTable.getSchema());
                preparedStatement.setString(2, dereferenceTable.getTableName());
                try (ResultSet resultSet = preparedStatement.executeQuery()) {
                    while (resultSet.next()) {
                        String columnName = resultSet.getString(COL_NAME);
                        result.put(columnName, true);
                    }
                }
            } catch (SQLException | NullPointerException exception) {
                return result;
            }
        }
        return result;
    }

    protected List<ContextColumn> findPrimaryKeyColumns() throws InitializationException {
        Clock clock = new Clock();
        ContextTable currentTable = this.getCurrentTable();
        List<ContextColumn> columns = new ArrayList<>();
        ContextTable dereferenceTable = this.dereference(currentTable);
        if (dereferenceTable.isOfType(ContextTable.TABLE)) {
            try (PreparedStatement preparedStatement = this.getPreparedStatement(SQL_TABLE_PK)) {
                preparedStatement.setString(1, dereferenceTable.getSchema());
                preparedStatement.setString(2, dereferenceTable.getTableName());
                try (ResultSet resultSet = preparedStatement.executeQuery()) {
                    while (resultSet.next()) {
                        String columnName = resultSet.getString(COL_NAME);
                        int pkOrdinal = resultSet.getInt(PK_ORDINAL);
                        ContextColumn column = this.findColumnByName(columnName);
                        if (column != null) {
                            column.setPkOrdinal(pkOrdinal);
                            columns.add(column);
                            clock.incrementCount();
                        }
                    }
                }
            } catch (SQLException | NullPointerException exception) {
                String message = Messages.getString("Could not access column(s) for table: '${tablename}' on '${datasourceUrl}'", "datasourceUrl", this.getConnectionDescriptor(), "tablename", String.valueOf(currentTable));
                throw new InitializationException(clock.getElapsedMessage(message), exception);
            }
        }

        if (clock.getCount() > 0) {
            LOG.debug(this.getCurrentTableName() + ' ' + clock.getElapsedMessageForCounter("Primary Key Column(s)"));
        } else {
            LOG.debug(this.getCurrentTableName() + ' ' + clock.getElapsedMessage("Found NO Primary Key Columns"));
        }
        return columns;
    }

    public ContextTable dereference(ContextTable table) {
        if (table.isOfType(ContextTable.SYNONYM) && table.getReference() != null) {
            return table.getReference();
        }
        return table;
    }

    protected Set<String> getEmptyCatalogs() {
        return this.getDatasource().getEmptyCatalogs();
//        Set<String> catalogs = new HashSet<>();
//        try (Statement statement = this.getConnection().createStatement()) {
//            boolean le8 = OracleDataPager.getDatabaseVersion(this.getConnection()) <= 8;
//            // oracle 8 以及以下版本的 sql
//            String oracleLE8SQL = "select u.username from all_users u , all_objects o where u.username = o.owner (+) group by u.username having count(object_name) = 0 order by u.username";
//            // oracle 8 以上版本的 sql
//            String oracleGT8SQL = "select u.username from all_users u left outer join all_objects o on u.username = o.owner group by u.username having count(object_name) = 0 order by u.username";
//            String sql = le8 ? oracleLE8SQL: oracleGT8SQL;
//            statement.setQueryTimeout(10);
//            try(ResultSet resultSet = statement.executeQuery(sql)) {
//                while (resultSet.next()) {
//                    String catalog = resultSet.getString(1);
//                    catalogs.add(catalog);
//                }
//            }
//        } catch (SQLException sqlException) {
//            sqlException.printStackTrace();
//        }
//        return catalogs;
    }

    public void setConnection(Connection connection) {
        // INSTANCE_NAME/DB_UNIQUE_NAME
        OracleDatasource datasource = (OracleDatasource) this.getDatasource();
        String dbnameType = datasource.getFetchDbNameType();
        try(PreparedStatement preparedStatement = connection.prepareStatement(
                "SELECT sys_context('USERENV','"+dbnameType+"') AS INSTANCE_NAME FROM dual"
        )){
            try(ResultSet resultSet = preparedStatement.executeQuery()){
                if (resultSet.next()) {
                    String instanceName = resultSet.getString("INSTANCE_NAME");
                    if(StringUtils.isBlank(instanceName)){
                        this.instanceName = this.getDatasource().getInventoryDbName();
                    } else{
                        this.instanceName = instanceName;
                    }
                }
            }
        } catch (Exception ignored){

        }
        super.setConnection(connection);
    }

    final Pattern pattern = Pattern.compile("\"([^\"]+)\"\\.\"([^\"]+)\"");

    protected String getCountSQLQuery(String qualifiedTableName) {
        Matcher matcher = pattern.matcher(qualifiedTableName);
        String schema = null;
        String tableName = null;
        if (matcher.matches()) {
            schema = matcher.group(1);
            tableName = matcher.group(2);
        }
        StringBuilder sql = new StringBuilder("select NUM_ROWS from all_tables " +
                "where owner = '"+schema+"' and table_name = '"+tableName+"'");
        return sql.toString();

//        if (this.getTaskGlobalDataHolder().getTaskParam().isTableRowCountEnabled()){
//            StringBuilder sql = new StringBuilder("select COUNT(1) FROM (select 1 from ");
//            sql.append(qualifiedTableName).append(" ) WHERE ROWNUM <=  ");
//            long rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getTableRowCountLimit();
//            sql.append(rowCountValue);
//            return sql.toString();
//        } else{
//            StringBuilder sql = new StringBuilder("select COUNT(1) FROM (select 1 from ");
//            sql.append(qualifiedTableName).append(" ) WHERE ROWNUM <=  ");
//            long rowCountValue = this.getTaskGlobalDataHolder().getTaskParam().getSampleCount()+1;
//            sql.append(rowCountValue);
//            return sql.toString();
////            return SELECT_COUNT + qualifiedTableName;
//        }
    }

    /**
     * 获取Oracle表容量查询SQL
     * 使用多种备选方案查询表的存储空间大小，按权限级别降级查询
     *
     * @param qualifiedTableName 限定表名，格式为 "schema"."table_name"
     * @return 表容量查询SQL
     */
    @Override
    protected String getTableCapacitySQLQuery(String qualifiedTableName) {
        Matcher matcher = pattern.matcher(qualifiedTableName);
        String schema = null;
        String tableName = null;
        if (matcher.matches()) {
            schema = matcher.group(1);
            tableName = matcher.group(2);
        }

        // 方案1: 优先尝试使用all_segments视图（需要较高权限）
        // 如果权限不足会在RuleContext中捕获异常并返回0
        StringBuilder sql = new StringBuilder("SELECT SUM(bytes) AS table_capacity " +
                "FROM all_segments " +
                "WHERE owner = '").append(schema).append("' " +
                "AND segment_name = '").append(tableName).append("' " +
                "AND segment_type = 'TABLE'");
        return sql.toString();
    }

    /**
     * 获取Oracle表容量查询SQL的备选方案
     * 当all_segments权限不足时，尝试使用user_segments视图
     *
     * @param qualifiedTableName 限定表名，格式为 "schema"."table_name"
     * @return 表容量查询SQL
     */
    protected String getTableCapacitySQLQueryFallback(String qualifiedTableName) {
        Matcher matcher = pattern.matcher(qualifiedTableName);
        String schema = null;
        String tableName = null;
        if (matcher.matches()) {
            schema = matcher.group(1);
            tableName = matcher.group(2);
        }

        // 方案2: 使用user_segments视图（权限要求较低，但只能查询当前用户的表）
        StringBuilder sql = new StringBuilder("SELECT SUM(bytes) AS table_capacity " +
                "FROM user_segments " +
                "WHERE segment_name = '").append(tableName).append("' " +
                "AND segment_type = 'TABLE'");
        return sql.toString();
    }

    /**
     * 重写表容量获取方法，实现Oracle特有的多级降级查询策略
     *
     * @param qualifiedTableName 限定表名
     * @return 表容量大小，如果获取失败或不支持则返回0
     */
    @Override
    protected long getTableCapacity(String qualifiedTableName) {
        long tableCapacity = 0L;

        // 方案1: 尝试使用all_segments视图
        String capacitySql = null;
        try {
            capacitySql = getTableCapacitySQLQuery(qualifiedTableName);
            if (org.apache.commons.lang.StringUtils.isNotBlank(capacitySql)) {
                tableCapacity = executeTableCapacityQuery(capacitySql, qualifiedTableName);
                if (tableCapacity > 0) {
                    return tableCapacity;
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to query table capacity using all_segments, trying fallback method: {}", e.getMessage());
        }

        // 方案2: 降级使用user_segments视图
        try {
            capacitySql = getTableCapacitySQLQueryFallback(qualifiedTableName);
            if (org.apache.commons.lang.StringUtils.isNotBlank(capacitySql)) {
                tableCapacity = executeTableCapacityQuery(capacitySql, qualifiedTableName);
                if (tableCapacity > 0) {
                    LOG.debug("Successfully retrieved table capacity using user_segments fallback method");
                    return tableCapacity;
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to query table capacity using user_segments fallback: {}", e.getMessage());
        }

        LOG.debug("Table capacity query not supported or failed for Oracle table: {}", qualifiedTableName);
        return 0L;
    }

    /**
     * 执行表容量查询的通用方法
     *
     * @param capacitySql 表容量查询SQL
     * @param qualifiedTableName 限定表名（用于日志）
     * @return 表容量大小
     */
    private long executeTableCapacityQuery(String capacitySql, String qualifiedTableName) {
        long tableCapacity = 0L;
        int sqlTimeoutSeconds = this.globalDataHolder.getTaskParam().getSampleSqlTimeout();
        LOG.debug("query Oracle table capacity SQL - {}", capacitySql);

        try (Statement statement = getStatement();
             ResultSet resultSet = SQLDataPager.executeTimed(true, statement, capacitySql, sqlTimeoutSeconds)) {
            LOG.debug("Done Executing Oracle table capacity sql - {}", capacitySql);
            if (resultSet.next()) {
                tableCapacity = resultSet.getLong(1);
            }
            return tableCapacity;
        } catch (SQLException exception) {
            // 对于ORA-00942错误（表或视图不存在），抛出异常让上层方法尝试降级方案
            if (exception.getMessage().contains("ORA-00942")) {
                throw new RuntimeException("Oracle segments view access denied: " + exception.getMessage(), exception);
            }
            // 其他SQL异常记录警告但不抛出异常
            LOG.warn("Could not get table capacity for: '{}' on '{}', running query is: {}",
                    qualifiedTableName, getDatasource().getInstanceName(), capacitySql);
            LOG.debug("Oracle table capacity query exception details", exception);
            return 0L;
        } catch (Exception e) {
            LOG.warn("Unexpected error during Oracle table capacity query for: '{}', query: {}",
                    qualifiedTableName, capacitySql);
            LOG.debug("Oracle table capacity query unexpected error details", e);
            return 0L;
        }
    }
}
