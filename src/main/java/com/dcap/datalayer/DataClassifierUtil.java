package com.dcap.datalayer;

import com.yd.dataclassifier.jni.*;
import com.yd.lib.utils.JniLoader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 数据分类器工具类
 * 封装了LibDataClassifier的初始化和使用逻辑
 */
public class DataClassifierUtil {
    private static final Logger logger = LoggerFactory.getLogger(DataClassifierUtil.class);

    // 全局数据目录
    private static final String DEFAULT_APP_DATA_DIR = "/dcap-log/dcap-probe-client/probe-client/jni_classifier";// System.getProperty("user.home") + File.separator+".dcap_classifier";
    private static final String DEFAULT_DATA_DIR = DEFAULT_APP_DATA_DIR + File.separator + "data";
    private static final String DEFAULT_CONFIG_DIR = DEFAULT_APP_DATA_DIR + File.separator + "config";
    private static final String DEFAULT_LOG_DIR = DEFAULT_APP_DATA_DIR + File.separator + "log";

    // 分类器配置文件名
    private static final String CLASSIFY_MODELS_YAML = "classify_models.yaml";
    private static final String DISCOVERY_MODELS_YAML = "discovery_models.yaml";
    private static final String CLASSIFY_DICTS_YAML = "classify_dicts.yaml";
    private static final String DATA_CLASSIFIER_YAML = "data_classifier.yaml";

    private String dataDir;
    private String defaultConfigDir;
    private String defaultLogDir;

    static {
        // 加载JNI库
        JniLoader.load(DEFAULT_DATA_DIR);
    }

    // 私有构造函数，防止外部实例化
    private DataClassifierUtil() {
        this.dataDir = DEFAULT_DATA_DIR;
        this.defaultConfigDir = DEFAULT_CONFIG_DIR;
        this.defaultLogDir = DEFAULT_LOG_DIR;
        // 创建必要的目录
        try {
            Files.createDirectories(Paths.get(dataDir));
            Files.createDirectories(Paths.get(defaultConfigDir));
            Files.createDirectories(Paths.get(defaultLogDir));
            logger.info("创建目录成功: data={}, config={}, log={}", dataDir, defaultConfigDir, defaultLogDir);

            // 创建默认的配置文件（如果不存在）
            createDefaultConfigFiles();
        } catch (IOException e) {
            logger.error("创建目录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建默认的配置文件
     * @throws IOException 如果创建文件失败
     */
    private void createDefaultConfigFiles() throws IOException {
        // 创建 classify_dicts.yaml
        File dictsFile = new File(defaultConfigDir, CLASSIFY_DICTS_YAML);
        if (!dictsFile.exists()) {
            try (FileWriter writer = new FileWriter(dictsFile)) {
                writer.write("dictionaries:\n");
                writer.write("- dict_id: D_FILE_INFORMATION\n");
                writer.write("  name: 文件信息词典\n");
                writer.write("  data:\n");
                writer.write("  - 文档\n");
                writer.write("  - 报告\n");
                writer.write("  - 合同\n");
            }
            logger.info("创建默认词典文件成功: {}", dictsFile.getAbsolutePath());
        }

        // 创建 data_classifier.yaml
        File dataClassifierFile = new File(defaultConfigDir, DATA_CLASSIFIER_YAML);
        if (!dataClassifierFile.exists()) {
            try (FileWriter writer = new FileWriter(dataClassifierFile)) {
                writer.write("data_classifier_config:\n");
                writer.write("  log_config:\n");
                writer.write("    enable: true\n");
                writer.write("    console_log: true\n");
                writer.write("    log_level: 'debug'\n\n");
                writer.write("  loading_policy:\n");
                writer.write("    mode: 'local'\n");
                writer.write("    classify_models_file: 'classify_models.yaml'\n");
                writer.write("    discovery_models_file: 'discovery_models.yaml'\n");
                writer.write("    classify_dict_file: 'classify_dicts.yaml'\n");
                writer.write("    auto_loading: true\n");
                writer.write("    poll_period_sec: 5\n");
            }
            logger.info("创建默认分类器配置文件成功: {}", dataClassifierFile.getAbsolutePath());
        }
    }

    /**
     * 静态内部类，延迟加载单例
     */
    private static class SingletonHolder {
        private static final DataClassifierUtil INSTANCE = new DataClassifierUtil();
    }

    /**
     * 获取单例实例
     * @return DataClassifierUtil实例
     */
    public static DataClassifierUtil getInstance() {
        return SingletonHolder.INSTANCE;
    }

    /**
     * 设置数据目录
     * @param dataDir 数据目录路径
     */
    public void setDataDir(String dataDir) {
        this.dataDir = dataDir;
        try {
            Files.createDirectories(Paths.get(dataDir));
            logger.info("创建数据目录成功: {}", dataDir);
        } catch (IOException e) {
            logger.error("创建数据目录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取数据目录
     * @return 数据目录路径
     */
    public String getDataDir() {
        return dataDir;
    }

    /**
     * 根据任务ID、模型定义和字典定义构建分类器
     * @param scanJobHistoryId 扫描任务历史ID
     * @param modelExpr 结构化模型定义表达式（classify_models.yaml的内容）
     * @param dictExpr 字典定义表达式（classify_dicts.yaml的内容）
     * @param tenantId 租户ID
     * @return 分类器指针，如果初始化失败则返回null
     */
    public synchronized LibDataClassifierPointer build(long scanJobHistoryId,
                                                       String modelExpr, String dictExpr, long tenantId) {
        return build(scanJobHistoryId, modelExpr, null, dictExpr, tenantId);
    }

    /**
     * 根据任务ID、模型定义和字典定义构建分类器
     * @param scanJobHistoryId 扫描任务历史ID
     * @param modelExpr 结构化模型定义表达式（classify_models.yaml的内容）
     * @param discoveryModelExpr 非结构化模型定义表达式（discovery_models.yaml的内容）
     * @param dictExpr 字典定义表达式（classify_dicts.yaml的内容）
     * @param tenantId 租户ID
     * @return 分类器指针，如果初始化失败则返回null
     */
    public synchronized LibDataClassifierPointer build(long scanJobHistoryId,
                                                       String modelExpr, String discoveryModelExpr, String dictExpr, long tenantId) {
        try {
            // 创建任务特定的目录
            String taskConfigDir = defaultConfigDir + File.separator + "task_" + scanJobHistoryId;

            // 创建目录
            Files.createDirectories(Paths.get(taskConfigDir));
            Files.createDirectories(Paths.get(DEFAULT_LOG_DIR));

            // 创建配置文件
            createTaskConfigFiles(taskConfigDir, modelExpr, discoveryModelExpr, dictExpr);

            LibDataClassifierPointer classifierPtr = new LibDataClassifierPointer();

            // 初始化配置
            lib_data_classifier_config config = new lib_data_classifier_config();
            config.setTenant_id(tenantId);
            config.setConfig_dir(taskConfigDir);
            config.setLog_dir(DEFAULT_LOG_DIR);
            config.setRemote_service("");
            config.setUser("");
            config.setPassword("");

            // 创建分类器实例
            DATACLS_RET ret = LibDataClassifier.new_lib_data_classifier(config, classifierPtr.cast());
            if (ret != DATACLS_RET.DATACLS_SUCCESS) {
                logger.error("创建分类器失败，错误码: {}", ret);
                return null;
            }

            // 启动分类器
            ret = LibDataClassifier.start_lib_data_classifier(classifierPtr.value());
            if (ret != DATACLS_RET.DATACLS_SUCCESS) {
                logger.error("启动分类器失败，错误码: {}", ret);
                return null;
            }

            // 设置日志级别
            LibDataClassifier.datacls_set_log_level(DATACLS_LOG_LEVEL.DATACLS_LOG_DEBUG);

            logger.info("任务 {} 的分类器初始化成功", scanJobHistoryId);
            return classifierPtr;
        } catch (Exception e) {
            logger.error("初始化任务 {} 的分类器失败: {}", scanJobHistoryId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建任务特定的配置文件
     * @param taskConfigDir 任务配置目录
     * @param modelExpr 结构化模型定义表达式（classify_models.yaml的内容）
     * @param discoveryModelExpr 非结构化模型定义表达式（discovery_models.yaml的内容）
     * @param dictExpr 字典定义表达式
     * @throws IOException 如果创建文件失败
     */
    private void createTaskConfigFiles(String taskConfigDir, String modelExpr,
                                       String discoveryModelExpr, String dictExpr) throws IOException {
        // 创建 classify_models.yaml
        if (modelExpr != null) {
            File modelsFile = new File(taskConfigDir, CLASSIFY_MODELS_YAML);
            try (FileWriter writer = new FileWriter(modelsFile)) {
                writer.write(modelExpr);
            }
            logger.info("创建结构化任务模型文件成功: {}", modelsFile.getAbsolutePath());
        }

        // 创建 discovery_models.yaml
        if (discoveryModelExpr != null) {
            File discoveryModelsFile = new File(taskConfigDir, DISCOVERY_MODELS_YAML);
            try (FileWriter writer = new FileWriter(discoveryModelsFile)) {
                writer.write(discoveryModelExpr);
            }
            logger.info("创建非结构化任务模型文件成功: {}", discoveryModelsFile.getAbsolutePath());
        }

        // 创建 classify_dicts.yaml
        File destDictsFile = new File(taskConfigDir, CLASSIFY_DICTS_YAML);
        if (StringUtils.isBlank(dictExpr) || Objects.equals(dictExpr, "null")) {
            dictExpr = "dictionaries:";
        }
        try (FileWriter writer = new FileWriter(destDictsFile)) {
            writer.write(dictExpr);
        }
        logger.info("创建任务字典文件成功: {}", destDictsFile.getAbsolutePath());

        // 复制 data_classifier.yaml
        File srcDataClassifierFile = new File(defaultConfigDir, DATA_CLASSIFIER_YAML);
        File destDataClassifierFile = new File(taskConfigDir, DATA_CLASSIFIER_YAML);
        if (srcDataClassifierFile.exists()) {
            Files.deleteIfExists(destDataClassifierFile.toPath()); // 删除已存在的文件
            Files.copy(srcDataClassifierFile.toPath(), destDataClassifierFile.toPath());
            logger.info("复制分类器配置文件成功: {} -> {}", srcDataClassifierFile.getAbsolutePath(), destDataClassifierFile.getAbsolutePath());
        } else {
            // 如果源文件不存在，创建一个默认的配置文件
            try (FileWriter writer = new FileWriter(destDataClassifierFile)) {
                writer.write("data_classifier_config:\n");
                writer.write("  log_config:\n");
                writer.write("    enable: true\n");
                writer.write("    console_log: true\n");
                writer.write("    log_level: 'debug'\n\n");
                writer.write("  loading_policy:\n");
                writer.write("    mode: 'local'\n");
                writer.write("    classify_models_file: 'classify_models.yaml'\n");
                writer.write("    discovery_models_file: 'discovery_models.yaml'\n");
                writer.write("    classify_dict_file: 'classify_dicts.yaml'\n");
                writer.write("    auto_loading: true\n");
                writer.write("    poll_period_sec: 5\n");
            }
            logger.info("创建默认分类器配置文件成功: {}", destDataClassifierFile.getAbsolutePath());
        }
    }

    /**
     * 释放任务的分类器资源
     * @param scanJobHistoryId 扫描任务历史ID
     * @param classifierPtr 分类器指针
     */
    public void releaseTaskClassifier(long scanJobHistoryId, LibDataClassifierPointer classifierPtr) {
        if (classifierPtr != null) {
            LibDataClassifier.delete_lib_data_classifier(classifierPtr.value());

            logger.info("释放任务 {} 的分类器资源成功", scanJobHistoryId);

            // 删除任务特定的目录
            try {
                String taskConfigDir = defaultConfigDir + "/task_" + scanJobHistoryId;
                String taskLogDir = defaultLogDir + "/task_" + scanJobHistoryId;

                // 删除配置目录
//                File configDir = new File(taskConfigDir);
//                if (configDir.exists() && configDir.isDirectory()) {
//                    for (File file : configDir.listFiles()) {
//                        file.delete();
//                    }
//                    configDir.delete();
//                }

                // 删除日志目录
//                File logDir = new File(taskLogDir);
//                if (logDir.exists() && logDir.isDirectory()) {
//                    for (File file : logDir.listFiles()) {
//                        file.delete();
//                    }
//                    logDir.delete();
//                }

                logger.info("删除任务 {} 的目录成功", scanJobHistoryId);
            } catch (Exception e) {
                logger.error("删除任务 {} 的目录失败: {}", scanJobHistoryId, e.getMessage(), e);
            }
        }
    }

    /**
     * 创建列数据对象
     * @param tableName 表名
     * @param columnName 列名
     * @param columnComment 列注释
     * @param columnType 列类型
     * @param values 列值数组
     * @return 列数据对象
     */
    public datacls_column_data createColumnData(String tableName,
                                               String columnName,
                                               String columnComment,
                                               DATACLS_COLUMN_TYPE columnType,
                                               String[] values) {
        // 创建列数据对象
        datacls_column_data columnData = new datacls_column_data();
        columnData.setTable_name(tableName);
        columnData.setColumn_name(columnName);
        columnData.setColumn_comment(columnComment);
        columnData.setColumn_type(columnType);

        // 设置样本数据
        datacls_string_array sampleData = new datacls_string_array();
        sampleData.createArray(values.length);
        for (int i = 0; i < values.length; i++) {
            sampleData.setString(i, values[i]);
        }
        columnData.setColumn_value(sampleData);

        return columnData;
    }

    /**
     * 对列数据进行分类
     * @param classifierPtr 分类器指针
     * @param columnData 列数据
     * @param scoreThreshold 分数阈值
     * @param hitRatioThreshold 命中率阈值
     * @param quickRegexMatch 是否快速正则匹配
     * @return 分类结果，如果分类失败则返回null
     */
    public datacls_classify_result classify(LibDataClassifierPointer classifierPtr,
                                           datacls_column_data columnData,
                                           int scoreThreshold,
                                           int hitRatioThreshold,
                                           boolean quickRegexMatch) {
        if (classifierPtr == null || columnData == null) {
            logger.warn("分类器指针或列数据为空");
            return null;
        }

        // 创建分类配置
        datacls_classify_config classifyConfig = new datacls_classify_config();
        classifyConfig.setScore_threshold(scoreThreshold);
        classifyConfig.setHit_ratio_threshold(hitRatioThreshold);
        classifyConfig.setQuick_regex_match(quickRegexMatch);
        classifyConfig.setColumn_name_case_insensitive(true);

        // 创建结果对象
        datacls_classify_result result = new datacls_classify_result();

        // 执行分类
        DATACLS_RET ret = LibDataClassifier.datacls_classify(classifierPtr.value(), columnData, classifyConfig, result);

        if (ret != DATACLS_RET.DATACLS_SUCCESS) {
            logger.error("分类失败，错误码: {}", ret);
            return null;
        }

        return result;
    }

    /**
     * 使用默认参数对列数据进行分类
     * @param classifierPtr 分类器指针
     * @param columnData 列数据
     * @return 分类结果，如果分类失败则返回null
     */
    public datacls_classify_result classify(LibDataClassifierPointer classifierPtr, datacls_column_data columnData) {
        return classify(classifierPtr, columnData, 30, 10, false);
    }

    /**
     * 检查分类结果中是否包含指定的业务标签
     * @param result 分类结果
     * @param expectedTag 期望的业务标签
     * @return 是否包含指定标签
     */
    public boolean containsBusinessTag(datacls_classify_result result, String expectedTag) {
        if (result == null || expectedTag == null) {
            return false;
        }

        if (result.getBiz_tags().getCount() > 0) {
            for (int i = 0; i < result.getBiz_tags().getCount(); i++) {
                String tag = result.getBiz_tags().getString(i);
                if (expectedTag.equals(tag)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 打印分类结果
     * @param result 分类结果
     */
    public void printClassifyResult(datacls_classify_result result) {
        if (result == null) {
            logger.info("分类结果为空");
            return;
        }

        // 打印业务标签
        if (result.getBiz_tags().getCount() > 0) {
            StringBuilder bizTags = new StringBuilder("业务标签: ");
            for (int i = 0; i < result.getBiz_tags().getCount(); i++) {
                bizTags.append(result.getBiz_tags().getString(i)).append(" ");
            }
            logger.info(bizTags.toString());
        } else {
            logger.info("未找到业务标签");
        }

        // 打印技术标签
        if (result.getTech_tags().getCount() > 0) {
            StringBuilder techTags = new StringBuilder("技术标签: ");
            for (int i = 0; i < result.getTech_tags().getCount(); i++) {
                techTags.append(result.getTech_tags().getString(i)).append(" ");
            }
            logger.info(techTags.toString());
        } else {
            logger.info("未找到技术标签");
        }
    }

    /**
     * 对非结构化文本进行发现
     * @param classifierPtr 分类器指针
     * @param text 要分析的文本
     * @return 发现结果，如果发现失败则返回null
     */
    public datacls_discovery_result discoveryText(LibDataClassifierPointer classifierPtr, String text) {
        if (classifierPtr == null || StringUtils.isBlank(text)) {
            logger.warn("分类器指针或文本为空");
            return null;
        }

        // 创建发现配置
        datacls_discovery_config discoveryConfig = new datacls_discovery_config();
        discoveryConfig.setQuick_regex_match(true);

        // 创建结果对象
        datacls_discovery_result result = new datacls_discovery_result();

        // 执行发现
        DATACLS_RET ret = LibDataClassifier.datacls_discovery_text(classifierPtr.value(), text, discoveryConfig, result);

        if (ret != DATACLS_RET.DATACLS_SUCCESS) {
            logger.error("文本发现失败，错误码: {}", ret);
            return null;
        }

        return result;
    }

    /**
     * 根据任务ID和非结构化模型定义构建分类器（使用默认字典）
     * @param scanJobHistoryId 扫描任务历史ID
     * @param discoveryModelExpr 非结构化模型定义表达式（discovery_models.yaml的内容）
     * @param tenantId 租户ID
     * @return 分类器指针，如果初始化失败则返回null
     */
    public synchronized LibDataClassifierPointer buildForDiscovery(long scanJobHistoryId, String discoveryModelExpr, long tenantId) {
        // 创建默认字典
        String dictExpr = "dictionaries:\n" +
                "- dict_id: D_FILE_INFORMATION\n" +
                "  name: 文件信息词典\n" +
                "  data:\n" +
                "  - 文档\n" +
                "  - 报告\n" +
                "  - 合同\n";
        return build(scanJobHistoryId, null, discoveryModelExpr, dictExpr, tenantId);
    }

    /**
     * 主方法，用于演示如何使用DataClassifierUtil工具类
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("===== 数据分类器工具类使用示例 =====");

        // 获取当前目录
        String currentDir = System.getProperty("user.dir");
        System.out.println("当前目录: " + currentDir);

        // 初始化目录路径
        String configDir = currentDir + "/config";
        String samplesDir = currentDir + "/data/samples";

        // 获取工具类实例
        DataClassifierUtil util = DataClassifierUtil.getInstance();

        // 打印默认数据目录
        System.out.println("默认数据目录: " + util.getDataDir());

        // 模拟任务ID
        long scanJobHistoryId = System.currentTimeMillis();
        System.out.println("模拟任务ID: " + scanJobHistoryId);

        // 读取模型定义
        StringBuilder modelExpr = new StringBuilder();
        StringBuilder dictExpr = new StringBuilder();
        try {
            // 读取模型定义
            File modelsFile = new File(configDir, "classify_models.yaml");
            if (!modelsFile.exists()) {
                System.err.println("模型文件不存在: " + modelsFile.getAbsolutePath());
                return;
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(modelsFile))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    modelExpr.append(line).append("\n");
                }
            }

            System.out.println("读取模型定义成功，长度: " + modelExpr.length() + " 字节");

            // 读取字典定义
            File dictsFile = new File(configDir, "classify_dicts.yaml");
            if (dictsFile.exists()) {
                try (BufferedReader reader = new BufferedReader(new FileReader(dictsFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        dictExpr.append(line).append("\n");
                    }
                }
                System.out.println("读取字典定义成功，长度: " + dictExpr.length() + " 字节");
            } else {
                System.out.println("字典文件不存在，将使用默认字典");
            }
        } catch (IOException e) {
            System.err.println("读取配置文件失败: " + e.getMessage());
            return;
        }



        // 构建分类器
        System.out.println("\n1. 构建分类器...");
        LibDataClassifierPointer classifierPtr = util.build(scanJobHistoryId, modelExpr.toString(), dictExpr.toString(), 1000);
        if (classifierPtr == null) {
            System.err.println("构建分类器失败，程序退出");
            return;
        }
        System.out.println("分类器构建成功");

        try {
            // 读取样本文件
            System.out.println("\n2. 读取样本文件...");
            File samplesFolder = new File(samplesDir);
            if (!samplesFolder.exists() || !samplesFolder.isDirectory()) {
                System.err.println("样本目录不存在: " + samplesDir);
                return;
            }

            File[] sampleFiles = samplesFolder.listFiles((dir, name) -> name.endsWith(".txt"));
            if (sampleFiles == null || sampleFiles.length == 0) {
                System.err.println("未找到样本文件");
                return;
            }

            System.out.println("找到 " + sampleFiles.length + " 个样本文件");

            // 处理每个样本文件
            for (File sampleFile : sampleFiles) {
                String fileName = sampleFile.getName();
                String fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
                String expectedTag = "T_" + fileNameWithoutExt.toUpperCase();

                System.out.println("\n处理样本文件: " + fileName + ", 期望标签: " + expectedTag);

                // 读取样本数据
                List<String> lines = new ArrayList<>();
                try (BufferedReader reader = new BufferedReader(new FileReader(sampleFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        line = line.trim();
                        if (!line.isEmpty()) {
                            lines.add(line);
                        }
                    }
                } catch (IOException e) {
                    System.err.println("读取样本文件失败: " + e.getMessage());
                    continue;
                }

                if (lines.isEmpty()) {
                    System.out.println("样本文件为空，跳过");
                    continue;
                }

                System.out.println("读取到 " + lines.size() + " 行样本数据");

                // 创建列数据
                String[] values = lines.toArray(new String[0]);
                datacls_column_data columnData = util.createColumnData(
                    "test_table",
                    fileNameWithoutExt,
                    "测试" + fileNameWithoutExt + "列",
                    DATACLS_COLUMN_TYPE.DATACLS_CT_STRING,
                    values
                );

                if (columnData == null) {
                    System.err.println("创建列数据失败");
                    continue;
                }

                // 执行分类
                System.out.println("3. 执行分类...");
                datacls_classify_result result = util.classify(classifierPtr, columnData);

                if (result == null) {
                    System.err.println("分类失败");
                    continue;
                }

                // 打印分类结果
                System.out.println("4. 分类结果:");

                // 打印业务标签
                if (result.getBiz_tags().getCount() > 0) {
                    System.out.print("业务标签: ");
                    for (int i = 0; i < result.getBiz_tags().getCount(); i++) {
                        System.out.print(result.getBiz_tags().getString(i) + " ");
                    }
                    System.out.println();
                } else {
                    System.out.println("未找到业务标签");
                }

                // 打印技术标签
                if (result.getTech_tags().getCount() > 0) {
                    System.out.print("技术标签: ");
                    for (int i = 0; i < result.getTech_tags().getCount(); i++) {
                        System.out.print(result.getTech_tags().getString(i) + " ");
                    }
                    System.out.println();
                } else {
                    System.out.println("未找到技术标签");
                }

                // 检查是否包含期望标签
                boolean containsExpectedTag = util.containsBusinessTag(result, expectedTag);
                System.out.println("是否包含期望标签 " + expectedTag + ": " + containsExpectedTag);
            }

        } finally {
            // 释放资源
            System.out.println("\n5. 释放资源...");
            util.releaseTaskClassifier(scanJobHistoryId, classifierPtr);
            System.out.println("资源释放完成");
        }

        System.out.println("\n===== 示例结束 =====");
    }
}
